# Jira任务批量创建工具

🚀 一个基于FastAPI的Web应用，支持通过Excel文件批量创建Jira任务，简化项目管理流程。

## 功能特性

- 📊 **Excel文件解析**: 支持.xlsx和.xls格式文件上传和解析，智能处理合并单元格
- 🔍 **任务预览**: 上传后可预览解析结果，支持任务选择和批量操作
- 🎯 **层次化创建**: 自动创建主任务和子任务的层次结构，支持需求关联
- 🔗 **Sprint关联**: 自动查询并关联指定的Sprint迭代
- 👥 **姓名映射管理**: 完整的姓名到OA账号映射系统，支持增删改查
- 📋 **结果导出**: 支持创建结果的Excel导出功能

## 技术栈

- **后端**: FastAPI + Python 3.8+
- **前端**: Bootstrap 5 + jQuery + Font Awesome
- **数据库**: MySQL 8.0+ (使用现有SRMS数据库)
- **文件处理**: pandas + openpyxl
- **Jira集成**: 直接HTTP API调用，支持多环境配置
- **日志系统**: 分级日志记录，支持API调用追踪

## 快速开始

### 1. 环境要求

- Python 3.8+
- MySQL 8.0+
- pip

### 2. 安装依赖

```bash
# 进入项目目录
cd jira-task-creator

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境

```bash
# 复制环境配置文件
cp env.example .env

# 编辑配置文件，配置数据库连接等信息
nano .env
```

### 4. 数据库配置

系统使用现有的SRMS数据库，需要确保以下表存在：

```sql
-- 姓名映射表 (如果不存在则创建)
CREATE TABLE IF NOT EXISTS t_jira_name_map (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '中文姓名',
    login VARCHAR(50) NOT NULL COMMENT 'OA账号',
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='JIRA姓名映射表';
```

### 5. 启动应用

```bash
# 开发模式启动
python run.py

# 或者使用uvicorn直接启动
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 6. 访问应用

- 🌐 **主应用**: http://localhost:8000
- 👥 **姓名映射管理**: http://localhost:8000/name-mapping
- ❓ **使用帮助**: http://localhost:8000/help

## 使用说明

### Excel文件格式

Excel文件必须包含以下列（列名必须完全一致）：

| 列名 | 是否必填 | 说明 | 示例 |
|------|----------|------|------|
| 橙卡 | 可选 | 需求的JIRA Key，用于关联需求 | JGKEZH-23876 |
| 模块 | 可选 | 功能模块名称 | 用户管理模块 |
| 主任务 | 必填 | 主任务描述，支持空值自动填充 | 实现用户登录功能 |
| 子任务 | 必填 | 子任务描述 | 设计登录页面UI |
| 任务类型 | 必填 | 任务类型，支持多种别名 | UI、API、后端、前端、测试 |
| 负责人 | 必填 | 负责人中文姓名 | 张三 |
| 工作量 | 可选 | 预估工作量（天） | 0.5、1、2 |




### 操作流程

1. **上传Excel文件**: 选择符合格式要求的Excel文件
2. **预览任务列表**: 检查解析结果，支持任务选择和层次展示
3. **配置JIRA连接**: 选择环境，输入认证信息和项目配置
4. **批量创建任务**: 系统按层次创建主任务和子任务
5. **查看创建结果**: 查看成功/失败统计，支持失败任务重试
6. **导出结果**: 可选择导出创建结果为Excel文件

### 姓名映射管理

- 访问 `/name-mapping` 页面管理姓名映射
- 支持添加、删除、搜索姓名映射关系
- 系统自动将Excel中的中文姓名转换为JIRA用户名
- 支持模糊搜索和批量管理

## API接口

### 核心接口

```http
# 解析Excel文件
POST /api/v1/parse-excel
Content-Type: multipart/form-data

# 批量创建JIRA任务
POST /api/v1/create-jira-tasks
Content-Type: application/json

# 测试JIRA连接
POST /api/v1/test-jira-connection
Content-Type: application/json

# 获取任务类型列表
GET /api/v1/task-types

# 姓名映射管理
GET /api/v1/jira/name-mapping
POST /api/v1/jira/name-mapping
DELETE /api/v1/jira/name-mapping/{id}

# 导出结果
POST /api/v1/export-results
```

### 请求示例

```json
{
  "jira_config": {
    "environment": "test",
    "username": "<EMAIL>",
    "token": "your_api_token",
    "project_key": "PROJ",
    "sprint": "INST2025-sprint10",
    "test_assignee": "zhouqishu"
  },
  "tasks": [
    {
      "demand": "JGKEZH-23876",
      "module": "用户管理",
      "main_task": "实现用户登录功能",
      "sub_task": "设计登录页面",
      "task_type": "UI",
      "assignee": "张三",
      "workload": "0.5"
    }
  ]
}
```

## 项目结构

```
jira-task-creator/
├── app/                    # 应用主目录
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── database.py        # 数据库连接管理
│   ├── routers/           # 路由模块
│   │   ├── __init__.py
│   │   ├── api.py         # API路由
│   │   └── web.py         # Web页面路由
│   ├── services/          # 业务服务层
│   │   ├── __init__.py
│   │   └── jira_service.py # JIRA服务
│   ├── static/            # 静态文件
│   │   ├── css/
│   │   │   └── style.css  # 自定义样式
│   │   └── js/
│   │       └── app.js     # 自定义脚本
│   └── templates/         # HTML模板
│       ├── base.html      # 基础模板
│       ├── index.html     # 主页
│       ├── preview.html   # 任务预览页
│       ├── result.html    # 结果页面
│       ├── name_mapping.html # 姓名映射管理
│       └── help.html      # 帮助页面
├── config/                # 配置模块
│   ├── __init__.py
│   ├── database.py        # 数据库配置
│   ├── jira.py           # JIRA环境配置
│   └── logging.py        # 日志配置
├── logs/                  # 日志文件目录
├── requirements.txt       # Python依赖
├── run.py                # 启动脚本
├── env.example           # 环境配置示例
└── README.md             # 项目说明
```


## 配置说明
详见.env


## 故障排除

### 常见问题

1. **Excel解析失败**: 检查列名是否完全一致，确保没有多余空格
2. **JIRA连接失败**: 验证API Token是否正确，网络是否可达
3. **姓名映射失败**: 确保数据库中存在对应的姓名映射记录
4. **任务创建失败**: 检查项目Key和Sprint名称是否正确

### 日志查看

```bash
# 查看应用日志
tail -f logs/app_$(date +%Y%m%d).log

# 查看JIRA API日志
tail -f logs/jira_api_$(date +%Y%m%d).log
```


---

⭐ 如果这个项目对您有帮助，请给我们一个星标！ 