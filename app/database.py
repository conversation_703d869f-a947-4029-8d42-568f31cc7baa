#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: database.py
描述: 数据库连接管理

更新日志:
2024-01-15 - 创建数据库连接管理模块
"""

import logging
from contextlib import contextmanager
from typing import Optional, Generator, Any, Dict, List
import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError

from config.database import db_config

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """初始化数据库引擎"""
        try:
            self.engine = create_engine(
                db_config.database_url,
                poolclass=QueuePool,
                pool_size=db_config.pool_size,
                max_overflow=db_config.max_overflow,
                pool_timeout=db_config.pool_timeout,
                pool_recycle=db_config.pool_recycle,
                echo=False,  # 设置为True可以看到SQL语句
                future=True
            )
            
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            logger.info("数据库引擎初始化成功")
            
        except Exception as e:
            logger.error(f"数据库引擎初始化失败: {str(e)}")
            raise
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """获取数据库会话的上下文管理器"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {str(e)}")
            raise
        finally:
            session.close()
    
    @contextmanager
    def get_connection(self) -> Generator[Any, None, None]:
        """获取原始数据库连接的上下文管理器"""
        connection = None
        try:
            connection = pymysql.connect(**db_config.get_connection_params())
            yield connection
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"数据库连接失败: {str(e)}")
            raise
        finally:
            if connection:
                connection.close()
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_session() as session:
                result = session.execute(text("SELECT 1"))
                return result.fetchone() is not None
        except Exception as e:
            logger.error(f"数据库连接测试失败: {str(e)}")
            return False
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """执行查询语句"""
        try:
            with self.get_session() as session:
                result = session.execute(text(query), params or {})
                columns = result.keys()
                rows = result.fetchall()
                return [dict(zip(columns, row)) for row in rows]
        except Exception as e:
            logger.error(f"查询执行失败: {str(e)}")
            raise
    
    def execute_update(self, query: str, params: Optional[Dict] = None) -> int:
        """执行更新语句"""
        try:
            with self.get_session() as session:
                result = session.execute(text(query), params or {})
                return result.rowcount
        except Exception as e:
            logger.error(f"更新执行失败: {str(e)}")
            raise
    
    def get_table_info(self, table_name: str) -> List[Dict]:
        """获取表结构信息"""
        query = """
        SELECT 
            COLUMN_NAME as column_name,
            DATA_TYPE as data_type,
            IS_NULLABLE as is_nullable,
            COLUMN_DEFAULT as column_default,
            COLUMN_COMMENT as column_comment
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = :schema AND TABLE_NAME = :table_name
        ORDER BY ORDINAL_POSITION
        """
        return self.execute_query(query, {
            'schema': db_config.database,
            'table_name': table_name
        })

# 全局数据库管理器实例
db_manager = DatabaseManager()
