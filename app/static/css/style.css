/* 自定义样式 */

/* 步骤指示器 */
.step-indicator {
    padding: 20px;
    margin-bottom: 20px;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    margin: 0 auto 10px;
    transition: all 0.3s ease;
}

.step-title {
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
}

.step-indicator.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.step-indicator.active .step-title {
    color: #0d6efd;
    font-weight: 600;
}

.step-indicator.completed .step-number {
    background-color: #198754;
    color: white;
}

.step-indicator.completed .step-title {
    color: #198754;
    font-weight: 600;
}

/* 文件上传区域 */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

/* 进度条增强 */
.progress {
    height: 25px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.progress-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 14px;
    border-radius: 15px;
}

/* 任务表格 */
.task-table {
    font-size: 14px;
}

.task-table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
    white-space: nowrap;
}

.task-table td {
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.task-table .status-badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
}

/* 状态徽章 */
.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
}

.status-processing {
    background-color: #cff4fc;
    color: #055160;
}

/* 卡片增强 */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: none;
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* 按钮增强 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 表单增强 */
.form-control {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
    color: #212529; /* 正式文字颜色 */
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Placeholder样式 - 更淡的颜色 */
.form-control::placeholder {
    color: #adb5bd !important; /* 更淡的灰色 */
    opacity: 1; /* 确保在所有浏览器中都显示 */
    font-style: italic; /* 斜体以进一步区分 */
}

.form-control::-webkit-input-placeholder {
    color: #adb5bd !important;
    opacity: 1;
    font-style: italic;
}

.form-control::-moz-placeholder {
    color: #adb5bd !important;
    opacity: 1;
    font-style: italic;
}

.form-control:-ms-input-placeholder {
    color: #adb5bd !important;
    opacity: 1;
    font-style: italic;
}

.form-control:-moz-placeholder {
    color: #adb5bd !important;
    opacity: 1;
    font-style: italic;
}

/* Textarea和Select的placeholder样式 */
.form-select::placeholder {
    color: #adb5bd !important;
    opacity: 1;
    font-style: italic;
}

textarea.form-control::placeholder {
    color: #adb5bd !important;
    opacity: 1;
    font-style: italic;
}

/* 确保输入框有值时的颜色对比 */
.form-control:not(:placeholder-shown) {
    color: #212529;
    font-style: normal;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

/* 导航栏增强 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 6px;
    margin: 0 4px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255,255,255,0.1);
}

/* 页脚 */
footer {
    margin-top: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .step-indicator {
        padding: 10px;
    }
    
    .step-number {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }
    
    .task-table {
        font-size: 12px;
    }
    
    .task-table td {
        max-width: 120px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示 */
.tooltip-inner {
    max-width: 200px;
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
} 