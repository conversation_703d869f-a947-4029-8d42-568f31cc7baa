#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: api.py
描述: API路由

更新日志:
2024-01-15 20:33 - 创建了API路由模块
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import JSONResponse, FileResponse
from typing import List, Dict, Any, Optional
import pandas as pd
import json
import logging
from datetime import datetime
import io
from app.database import db_manager
from config.jira import jira_config
from app.services.jira_service import jira_service
from app.services.retry_service import retry_service

# 创建路由实例
router = APIRouter()

def normalize_task_type(task_type: str) -> str:
    """
    标准化任务类型，支持多种别名映射
    
    Args:
        task_type: 原始任务类型
        
    Returns:
        标准化后的任务类型
    """
    if not task_type:
        return ""
    
    # 去除首尾空格并转换为小写进行比较
    task_type_lower = task_type.strip().lower()
    
    # 任务类型映射表
    task_type_mapping = {
        # 后端相关
        "后端": "API",
        "backend": "API",
        "api": "API",
        "接口": "API",
        "服务端": "API",
        
        # 前端相关  
        "前端": "UI",
        "frontend": "UI",
        "ui": "UI",
        "界面": "UI",
        "页面": "UI",
        
        # 测试相关
        "测试": "测试",
        "test": "测试",
        "qa": "测试",
        "质量保证": "测试",
        
        # 数据相关
        "数据": "数据",
        "data": "数据",
        "数据库": "数据",
        "database": "数据",
    }
    
    # 查找映射
    for key, value in task_type_mapping.items():
        if task_type_lower == key.lower():
            return value
    
    # 如果没有找到映射，返回原始值（首字母大写）
    return task_type.strip()

@router.post("/v1/parse-excel")
async def parse_excel(
    file: UploadFile = File(...),
    sheet_name: Optional[str] = Form(None)
):
    """
    解析Excel文件，返回任务列表供预览
    
    参数:
    - file: Excel文件
    - sheet_name: 工作表名称（可选）
    
    返回:
    - 解析后的任务列表
    """
    try:
        # 验证文件类型
        if not file.filename or not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=400, 
                detail="文件格式不支持，请上传Excel文件（.xlsx或.xls）"
            )
        
        # 读取Excel文件
        content = await file.read()

        # 解析Excel内容
        try:
            # 使用BytesIO包装字节内容
            excel_buffer = io.BytesIO(content)
            if sheet_name:
                df = pd.read_excel(excel_buffer, sheet_name=sheet_name)
            else:
                df = pd.read_excel(excel_buffer)
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Excel文件解析失败: {str(e)}"
            )
        
        # 打印实际的列名用于调试
        actual_columns = list(df.columns)
        logging.info(f"Excel文件实际列名: {actual_columns}")

        # 验证必要的列是否存在 - 使用更灵活的匹配（移除迭代字段）
        required_columns = ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量']

        # 创建列名映射，支持去除空格和不同的列名变体
        column_mapping = {}
        for req_col in required_columns:
            found = False
            for actual_col in actual_columns:
                # 去除空格并比较
                if str(actual_col).strip() == req_col:
                    column_mapping[req_col] = actual_col
                    found = True
                    break
            if not found:
                # 尝试一些常见的变体
                if req_col == '橙卡':
                    for actual_col in actual_columns:
                        if '橙卡' in str(actual_col) or 'demand' in str(actual_col).lower():
                            column_mapping[req_col] = actual_col
                            found = True
                            break
                elif req_col == '任务类型':
                    for actual_col in actual_columns:
                        if '任务类型' in str(actual_col) or '类型' in str(actual_col):
                            column_mapping[req_col] = actual_col
                            found = True
                            break


        missing_columns = [col for col in required_columns if col not in column_mapping]

        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Excel文件缺少必要的列: {', '.join(missing_columns)}。实际列名: {', '.join(actual_columns)}"
            )

        logging.info(f"列名映射: {column_mapping}")
        
        # 处理合并单元格 - 向前填充空值
        merge_columns = ['橙卡', '模块', '主任务']
        for col_name in merge_columns:
            if col_name in column_mapping:
                actual_col = column_mapping[col_name]
                df[actual_col] = df[actual_col].ffill()

        logging.info(f"处理合并单元格后的数据预览:\n{df.head()}")

        # 转换数据格式 - 使用英文字段名作为API标准格式
        tasks = []
        current_demand = ""
        current_module = ""
        current_main_task = ""

        for index, row in df.iterrows():
            # 获取当前行的值
            demand_val = row[column_mapping['橙卡']]
            demand = str(demand_val) if pd.notna(demand_val) else ""
            
            module_val = row[column_mapping['模块']]
            module = str(module_val) if pd.notna(module_val) else ""
            
            main_task_val = row[column_mapping['主任务']]
            main_task = str(main_task_val) if pd.notna(main_task_val) else ""
            
            sub_task_val = row[column_mapping['子任务']]
            sub_task = str(sub_task_val) if pd.notna(sub_task_val) else ""

            # 判断是否是新的分组
            is_new_demand = demand != current_demand and demand != ""
            is_new_module = module != current_module and module != ""
            is_new_main_task = main_task != current_main_task and main_task != ""

            # 更新当前分组状态
            if is_new_demand:
                current_demand = demand
            if is_new_module:
                current_module = module
            if is_new_main_task:
                current_main_task = main_task

            task_type_val = row[column_mapping['任务类型']]
            task_type = str(task_type_val) if pd.notna(task_type_val) else ""
            
            # 任务类型自动转换和标准化
            task_type = normalize_task_type(task_type)
            
            assignee_val = row[column_mapping['负责人']]
            assignee = str(assignee_val) if pd.notna(assignee_val) else ""
            
            workload_val = row[column_mapping['工作量']]
            workload = str(workload_val) if pd.notna(workload_val) else ""

            task = {
                "row": int(index) + 2,
                "demand": demand,
                "module": module,
                "main_task": main_task,
                "sub_task": sub_task,
                "task_type": task_type,
                "assignee": assignee,
                "workload": workload,
                # 添加分组标识
                "is_new_demand": is_new_demand,
                "is_new_module": is_new_module,
                "is_new_main_task": is_new_main_task,
                "group_level": 3 if sub_task else (2 if main_task else (1 if module else 0))
            }
            tasks.append(task)
        
        return {
            "success": True,
            "message": f"成功解析{len(tasks)}个任务",
            "data": {
                "filename": file.filename,
                "total_count": len(tasks),
                "tasks": tasks,
                "parsed_at": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误: {str(e)}"
        )









@router.post("/v1/create-jira-tasks")
async def create_jira_tasks(request_data: Dict[str, Any]):
    """批量创建Jira任务"""
    try:
        tasks = request_data.get('tasks', [])
        jira_config = request_data.get('jira_config', {})

        if not tasks:
            raise HTTPException(
                status_code=400,
                detail="没有要创建的任务"
            )

        # 验证Jira配置
        required_fields = ['environment', 'username', 'token', 'project_key', 'sprint']
        for field in required_fields:
            if field not in jira_config or not jira_config[field]:
                raise HTTPException(
                    status_code=400,
                    detail=f"缺少必要的任务配置: {field}"
                )

        # 获取环境URL
        from config.jira import jira_config as jira_env_config
        environment = jira_config['environment']
        jira_url = jira_env_config.get_environment_url(environment)

        if not jira_url:
            raise HTTPException(
                status_code=400,
                detail=f"无效的环境配置: {environment}"
            )

        # 记录批量创建请求详细信息
        logging.info("=" * 80)
        logging.info("收到JIRA批量任务创建请求:")
        logging.info(f"任务数量: {len(tasks)}")
        logging.info(f"环境: {jira_config['environment']}")
        logging.info(f"用户: {jira_config['username']}")
        logging.info(f"项目: {jira_config['project_key']}")
        logging.info(f"Sprint: {jira_config['sprint']}")
        logging.info("JIRA配置 (敏感信息已隐藏):")
        safe_config = {k: v if k != 'token' else '***' for k, v in jira_config.items()}
        logging.info(json.dumps(safe_config, indent=2, ensure_ascii=False))
        logging.info("任务详情:")
        for i, task in enumerate(tasks):
            logging.info(f"  任务 {i+1}: {task.get('sub_task') or task.get('main_task', 'unknown')} - 负责人: {task.get('assignee', 'unassigned')}")
        logging.info("=" * 80)

        # 使用JIRA服务创建任务（新的层次结构方法）
        result = jira_service.batch_create_issues_with_hierarchy(jira_config, tasks)

        success_tasks = result.get('success_tasks', [])
        failed_tasks = result.get('failed_tasks', [])

        # 记录批量创建结果
        logging.info("=" * 80)
        logging.info("JIRA批量任务创建结果:")
        logging.info(f"总任务数: {result.get('total', 0)}")
        logging.info(f"主任务创建: {result.get('main_tasks_created', 0)} 个")
        logging.info(f"子任务创建: {result.get('sub_tasks_created', 0)} 个")
        logging.info(f"Sprint关联: {result.get('sprint_linked', 0)} 个")
        logging.info(f"需求关联: {result.get('demands_linked', 0)} 个")
        logging.info(f"成功: {len(success_tasks)} 个")
        logging.info(f"失败: {len(failed_tasks)} 个")

        response_data = {
            "success": result.get('success', True),
            "data": {
                "success": success_tasks,
                "failed": failed_tasks,
                "summary": {
                    "total": result.get('total', 0),
                    "success_count": len(success_tasks),
                    "failed_count": len(failed_tasks),
                    "main_tasks_created": result.get('main_tasks_created', 0),
                    "sub_tasks_created": result.get('sub_tasks_created', 0),
                    "sprint_linked": result.get('sprint_linked', 0),
                    "demands_linked": result.get('demands_linked', 0)
                }
            }
        }

        logging.info("返回结果:")
        logging.info(json.dumps(response_data, indent=2, ensure_ascii=False))
        logging.info("=" * 80)

        # 如果是重试任务，更新重试记录
        if tasks and tasks[0].get('is_retry') and tasks[0].get('batch_id'):
            batch_id = tasks[0].get('batch_id')
            try:
                retry_service.update_retry_result(batch_id, response_data)
                logging.info(f"已更新重试批次 {batch_id} 的结果")
            except Exception as e:
                logging.error(f"更新重试记录失败: {str(e)}")

        return response_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"创建任务失败: {str(e)}"
        )

@router.post("/v1/export-results")
async def export_results(request_data: Dict[str, Any]):
    """导出创建结果"""
    try:
        results = request_data.get('results', {})
        include_success = request_data.get('include_success', True)
        include_failed = request_data.get('include_failed', True)
        include_summary = request_data.get('include_summary', True)

        # 创建导出数据
        export_data = []
        
        # 添加成功任务
        if include_success and results.get('success'):
            for task in results['success']:
                export_data.append({
                    '状态': '成功',
                    '任务类型': task.get('type', ''),
                    '任务标题': task.get('title', ''),
                    'JIRA编号': task.get('key', ''),
                    '负责人': task.get('assignee', ''),
                    '任务类型标识': task.get('task_type', ''),
                    'JIRA链接': task.get('url', ''),
                    '错误信息': ''
                })
        
        # 添加失败任务
        if include_failed and results.get('failed'):
            for task in results['failed']:
                export_data.append({
                    '状态': '失败',
                    '任务类型': task.get('type', ''),
                    '任务标题': task.get('title', ''),
                    'JIRA编号': '',
                    '负责人': task.get('assignee', ''),
                    '任务类型标识': task.get('task_type', ''),
                    'JIRA链接': '',
                    '错误信息': task.get('error', '')
                })
        
        # 创建DataFrame
        df = pd.DataFrame(export_data)
        
        # 生成文件名
        filename = f"jira_tasks_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = f"app/templates/{filename}"
        
        # 确保目录存在
        import os
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # 使用ExcelWriter创建多工作表Excel文件
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # 写入任务结果
            df.to_excel(writer, sheet_name='任务结果', index=False)
            
            # 添加汇总信息
            if include_summary and results.get('summary'):
                summary_data = []
                summary = results['summary']
                summary_data.append(['总任务数', summary.get('total', 0)])
                summary_data.append(['成功任务数', summary.get('success_count', 0)])
                summary_data.append(['失败任务数', summary.get('failed_count', 0)])
                summary_data.append(['主任务创建数', summary.get('main_tasks_created', 0)])
                summary_data.append(['子任务创建数', summary.get('sub_tasks_created', 0)])
                summary_data.append(['Sprint关联数', summary.get('sprint_linked', 0)])
                summary_data.append(['需求关联数', summary.get('demands_linked', 0)])
                
                summary_df = pd.DataFrame(summary_data, columns=['项目', '数量'])
                summary_df.to_excel(writer, sheet_name='汇总信息', index=False)

        return {
            "success": True,
            "filename": filename,
            "download_url": f"/api/v1/download/{filename}",
            "message": "导出成功"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"导出失败: {str(e)}"
        )



@router.get("/v1/jira/environments")
async def get_jira_environments():
    """获取JIRA环境配置"""
    try:
        from config.jira import jira_config
        environments = jira_config.get_all_environments()
        return {
            "success": True,
            "data": environments
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取JIRA环境配置失败: {str(e)}"
        )

@router.get("/v1/jira/name-mapping")
async def get_name_mappings():
    """获取姓名到OA账号的映射列表"""
    try:
        query = "SELECT id, name, login FROM t_jira_name_map ORDER BY name"
        mappings = db_manager.execute_query(query)
        return {
            "success": True,
            "data": mappings,
            "count": len(mappings)
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取姓名映射失败: {str(e)}"
        )

@router.post("/v1/jira/name-mapping")
async def add_name_mapping(request_data: Dict[str, Any]):
    """添加姓名到OA账号的映射"""
    try:
        name = request_data.get('name', '').strip()
        login = request_data.get('login', '').strip()

        if not name or not login:
            raise HTTPException(
                status_code=400,
                detail="姓名和OA账号不能为空"
            )

        # 检查是否已存在
        check_query = "SELECT id FROM t_jira_name_map WHERE name = :name"
        existing = db_manager.execute_query(check_query, {'name': name})

        if existing:
            raise HTTPException(
                status_code=400,
                detail=f"姓名 '{name}' 的映射已存在"
            )

        # 插入新映射
        insert_query = "INSERT INTO t_jira_name_map (name, login) VALUES (:name, :login)"
        db_manager.execute_update(insert_query, {'name': name, 'login': login})

        return {
            "success": True,
            "message": f"成功添加姓名映射: {name} -> {login}"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"添加姓名映射失败: {str(e)}"
        )

@router.get("/v1/jira/name-mapping/search")
async def search_name_mappings(q: str = ""):
    """搜索姓名到OA账号的映射"""
    try:
        if not q or not q.strip():
            # 如果搜索词为空，返回所有映射
            query = "SELECT id, name, login FROM t_jira_name_map ORDER BY name"
            mappings = db_manager.execute_query(query)
        else:
            # 模糊搜索姓名和OA账号
            search_term = f"%{q.strip()}%"
            query = """
                SELECT id, name, login FROM t_jira_name_map
                WHERE name LIKE :search_term OR login LIKE :search_term
                ORDER BY name
            """
            mappings = db_manager.execute_query(query, {'search_term': search_term})

        return {
            "success": True,
            "data": mappings,
            "count": len(mappings),
            "search_term": q.strip()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"搜索姓名映射失败: {str(e)}"
        )



@router.delete("/v1/jira/name-mapping/{mapping_id}")
async def delete_name_mapping(mapping_id: int):
    """删除姓名到OA账号的映射"""
    try:
        # 检查映射是否存在
        check_query = "SELECT name FROM t_jira_name_map WHERE id = :id"
        existing = db_manager.execute_query(check_query, {'id': mapping_id})

        if not existing:
            raise HTTPException(
                status_code=404,
                detail=f"ID为 {mapping_id} 的映射不存在"
            )

        name = existing[0]['name']

        # 删除映射
        delete_query = "DELETE FROM t_jira_name_map WHERE id = :id"
        db_manager.execute_update(delete_query, {'id': mapping_id})

        return {
            "success": True,
            "message": f"成功删除姓名映射: {name}"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"删除姓名映射失败: {str(e)}"
        )

@router.get("/v1/download/{filename}")
async def download_file(filename: str):
    """下载导出的文件"""
    try:
        import os
        filepath = f"app/templates/{filename}"
        
        if not os.path.exists(filepath):
            raise HTTPException(
                status_code=404,
                detail="文件不存在"
            )
        
        return FileResponse(
            path=filepath,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"下载文件失败: {str(e)}"
        )

@router.post("/v1/retry-failed-tasks")
async def retry_failed_tasks(request_data: Dict[str, Any]):
    """
    返回需要重试的任务数据，保留任务层次关系，并包含成功任务的JIRA链接信息
    """
    try:
        failed_tasks = request_data.get('failed_tasks', [])
        success_tasks = request_data.get('success_tasks', [])  # 获取成功任务列表

        if not failed_tasks:
            raise HTTPException(
                status_code=400,
                detail="没有需要重试的任务"
            )

        # 创建重试批次记录
        batch_id = retry_service.create_retry_batch(failed_tasks, success_tasks)

        # 构建成功任务的映射，用于查找已创建的任务链接
        success_map = {}
        if success_tasks:
            for success_task in success_tasks:
                # 使用主任务名称作为键
                main_task_key = success_task.get('main_task', '').strip()
                if main_task_key:
                    if main_task_key not in success_map:
                        success_map[main_task_key] = []
                    success_map[main_task_key].append({
                        'key': success_task.get('key', ''),
                        'url': success_task.get('url', ''),
                        'type': success_task.get('type', ''),
                        'title': success_task.get('title', ''),
                        'sub_task': success_task.get('sub_task', '')
                    })

        # 分析失败任务，提取需要重试的数据
        retry_tasks = []
        current_demand = ""
        current_module = ""
        current_main_task = ""

        for task in failed_tasks:
            demand = task.get('demand', '').strip()
            module = task.get('module', '').strip()
            main_task = task.get('main_task', '').strip()
            sub_task = task.get('sub_task', '').strip()

            # 判断是否是新的分组
            is_new_demand = demand != current_demand and demand != ""
            is_new_module = module != current_module and module != ""
            is_new_main_task = main_task != current_main_task and main_task != ""

            # 更新当前分组状态
            if is_new_demand:
                current_demand = demand
            if is_new_module:
                current_module = module
            if is_new_main_task:
                current_main_task = main_task

            # 查找相关的成功任务信息
            related_success_tasks = success_map.get(main_task, [])
            main_task_success = None
            sub_task_success = None

            # 查找主任务的成功记录
            for success_task in related_success_tasks:
                if success_task['type'] == 'main_task':
                    main_task_success = success_task
                elif success_task['type'] == 'sub_task' and success_task['sub_task'] == sub_task:
                    sub_task_success = success_task

            # 构造重试任务数据
            retry_task = {
                "demand": demand,
                "module": module,
                "main_task": main_task,
                "sub_task": sub_task,
                "assignee": task.get('assignee', ''),
                "workload": task.get('workload', ''),
                "task_type": task.get('task_type', ''),
                "is_new_demand": is_new_demand,
                "is_new_module": is_new_module,
                "is_new_main_task": is_new_main_task,
                "group_level": 3 if sub_task else (2 if main_task else (1 if module else 0)),
                # 添加成功任务的链接信息
                "main_task_success": main_task_success,
                "sub_task_success": sub_task_success,
                # 保持向后兼容
                "success_key": task.get('key', ''),
                "success_url": task.get('url', ''),
                # 添加重试标识
                "is_retry": True,
                "original_error": task.get('error', ''),
                "batch_id": batch_id
            }
            retry_tasks.append(retry_task)

        return {
            "success": True,
            "data": retry_tasks,
            "retry_mode": True,
            "batch_id": batch_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取重试任务失败: {str(e)}"
        )


@router.get("/v1/retry-history")
async def get_retry_history(batch_id: Optional[str] = None, limit: int = 100):
    """
    获取重试历史记录
    """
    try:
        history = retry_service.get_retry_history(batch_id, limit)
        return {
            "success": True,
            "data": history
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取重试历史失败: {str(e)}"
        )
