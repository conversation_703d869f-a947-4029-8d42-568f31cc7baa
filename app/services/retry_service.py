#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: retry_service.py
描述: 重试服务，管理任务重试逻辑
"""

import uuid
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from app.database import db_manager
from app.models.retry_history import RetryHistory, CREATE_TABLE_SQL

# 配置日志
logger = logging.getLogger(__name__)

class RetryService:
    """重试服务类"""
    
    def __init__(self):
        self._ensure_table_exists()
    
    def _ensure_table_exists(self):
        """确保重试历史表存在"""
        try:
            db_manager.execute_update(CREATE_TABLE_SQL)
            logger.info("重试历史表检查/创建完成")
        except Exception as e:
            logger.error(f"创建重试历史表失败: {str(e)}")
    
    def create_retry_batch(self, failed_tasks: List[Dict[str, Any]], 
                          success_tasks: List[Dict[str, Any]] = None) -> str:
        """
        创建重试批次记录
        
        Args:
            failed_tasks: 失败的任务列表
            success_tasks: 成功的任务列表（用于构建链接信息）
            
        Returns:
            batch_id: 批次ID
        """
        batch_id = str(uuid.uuid4())
        
        try:
            # 构建成功任务的映射
            success_map = {}
            if success_tasks:
                for success_task in success_tasks:
                    main_task_key = success_task.get('main_task', '').strip()
                    if main_task_key:
                        if main_task_key not in success_map:
                            success_map[main_task_key] = []
                        success_map[main_task_key].append(success_task)
            
            # 为每个失败任务创建重试记录
            retry_records = []
            for task in failed_tasks:
                main_task = task.get('main_task', '').strip()
                related_success_tasks = success_map.get(main_task, [])
                
                # 查找相关的成功任务信息
                main_task_success = None
                sub_task_success = None
                for success_task in related_success_tasks:
                    if success_task.get('type') == 'main_task':
                        main_task_success = success_task
                    elif (success_task.get('type') == 'sub_task' and 
                          success_task.get('sub_task') == task.get('sub_task')):
                        sub_task_success = success_task
                
                # 构建重试任务数据
                retry_task_data = {
                    **task,
                    'main_task_success': main_task_success,
                    'sub_task_success': sub_task_success,
                    'is_retry': True,
                    'batch_id': batch_id
                }
                
                retry_record = RetryHistory.create_retry_record(
                    batch_id=batch_id,
                    original_task=task,
                    retry_task=retry_task_data,
                    retry_reason=task.get('error', '原始创建失败')
                )
                retry_records.append(retry_record)
            
            # 批量插入数据库
            self._save_retry_records(retry_records)
            
            logger.info(f"创建重试批次 {batch_id}，包含 {len(retry_records)} 个任务")
            return batch_id
            
        except Exception as e:
            logger.error(f"创建重试批次失败: {str(e)}")
            raise
    
    def _save_retry_records(self, retry_records: List[RetryHistory]):
        """保存重试记录到数据库"""
        try:
            insert_sql = """
            INSERT INTO retry_history (batch_id, original_task_data, retry_task_data, 
                                     retry_reason, retry_status, created_at)
            VALUES (%(batch_id)s, %(original_task_data)s, %(retry_task_data)s, 
                    %(retry_reason)s, %(retry_status)s, %(created_at)s)
            """
            
            records_data = []
            for record in retry_records:
                records_data.append({
                    'batch_id': record.batch_id,
                    'original_task_data': record.original_task_data,
                    'retry_task_data': record.retry_task_data,
                    'retry_reason': record.retry_reason,
                    'retry_status': record.retry_status,
                    'created_at': datetime.now()
                })
            
            # 批量插入
            with db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.executemany(insert_sql, records_data)
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"保存重试记录失败: {str(e)}")
            raise
    
    def update_retry_result(self, batch_id: str, results: Dict[str, Any]):
        """
        更新重试结果
        
        Args:
            batch_id: 批次ID
            results: 重试结果
        """
        try:
            # 根据结果确定状态
            status = 'success' if results.get('success', False) else 'failed'
            
            update_sql = """
            UPDATE retry_history 
            SET retry_status = %(status)s, retry_result = %(result)s, updated_at = %(updated_at)s
            WHERE batch_id = %(batch_id)s
            """
            
            db_manager.execute_update(update_sql, {
                'status': status,
                'result': str(results),
                'updated_at': datetime.now(),
                'batch_id': batch_id
            })
            
            logger.info(f"更新重试批次 {batch_id} 结果: {status}")
            
        except Exception as e:
            logger.error(f"更新重试结果失败: {str(e)}")
            raise
    
    def get_retry_history(self, batch_id: Optional[str] = None, 
                         limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取重试历史记录
        
        Args:
            batch_id: 批次ID（可选）
            limit: 限制返回数量
            
        Returns:
            重试历史记录列表
        """
        try:
            if batch_id:
                query_sql = """
                SELECT * FROM retry_history 
                WHERE batch_id = %(batch_id)s 
                ORDER BY created_at DESC
                """
                params = {'batch_id': batch_id}
            else:
                query_sql = """
                SELECT * FROM retry_history 
                ORDER BY created_at DESC 
                LIMIT %(limit)s
                """
                params = {'limit': limit}
            
            return db_manager.execute_query(query_sql, params)
            
        except Exception as e:
            logger.error(f"获取重试历史失败: {str(e)}")
            return []

# 全局重试服务实例
retry_service = RetryService()
