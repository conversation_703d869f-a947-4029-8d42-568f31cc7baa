{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="text-center mb-4">
            <h1 class="display-6">
                <i class="fas fa-history text-info me-2"></i>
                重试历史记录
            </h1>
            <p class="lead text-muted">查看任务重试的历史记录和结果</p>
        </div>

        <!-- 筛选器 -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="batchIdFilter" class="form-label">批次ID</label>
                        <input type="text" class="form-control" id="batchIdFilter" placeholder="输入批次ID筛选">
                    </div>
                    <div class="col-md-4">
                        <label for="statusFilter" class="form-label">状态</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="pending">待处理</option>
                            <option value="success">成功</option>
                            <option value="failed">失败</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="button" class="btn btn-primary me-2" id="searchBtn">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="refreshBtn">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史记录表格 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>重试记录
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>批次ID</th>
                                <th>重试时间</th>
                                <th>任务信息</th>
                                <th>重试原因</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="historyTableBody">
                            <!-- 动态加载内容 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 加载状态 -->
                <div id="loadingIndicator" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载重试历史...</p>
                </div>
                
                <!-- 空状态 -->
                <div id="emptyState" class="text-center py-5" style="display: none;">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无重试记录</h5>
                    <p class="text-muted">还没有任何任务重试记录</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">重试详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="detailContent">
                    <!-- 动态加载详情内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    let currentData = [];
    
    // 加载重试历史
    function loadRetryHistory(batchId = '', status = '') {
        $('#loadingIndicator').show();
        $('#emptyState').hide();
        
        let url = '/api/v1/retry-history?limit=100';
        if (batchId) {
            url += `&batch_id=${encodeURIComponent(batchId)}`;
        }
        
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                $('#loadingIndicator').hide();
                
                if (response.success && response.data && response.data.length > 0) {
                    currentData = response.data;
                    
                    // 根据状态筛选
                    let filteredData = currentData;
                    if (status) {
                        filteredData = currentData.filter(item => item.retry_status === status);
                    }
                    
                    displayHistory(filteredData);
                } else {
                    $('#emptyState').show();
                    $('#historyTableBody').empty();
                }
            },
            error: function(xhr) {
                $('#loadingIndicator').hide();
                let errorMessage = '加载重试历史失败';
                if (xhr.responseJSON && xhr.responseJSON.detail) {
                    errorMessage = xhr.responseJSON.detail;
                }
                alert(errorMessage);
            }
        });
    }
    
    // 显示历史记录
    function displayHistory(data) {
        const tbody = $('#historyTableBody');
        tbody.empty();
        
        data.forEach(function(item) {
            const createdAt = new Date(item.created_at).toLocaleString('zh-CN');
            const statusBadge = getStatusBadge(item.retry_status);
            
            // 解析任务数据
            let taskInfo = '未知任务';
            try {
                const taskData = JSON.parse(item.retry_task_data || '{}');
                if (taskData.main_task && taskData.sub_task) {
                    taskInfo = `${taskData.main_task} - ${taskData.sub_task}`;
                } else if (taskData.main_task) {
                    taskInfo = taskData.main_task;
                }
            } catch (e) {
                console.error('解析任务数据失败:', e);
            }
            
            const row = `
                <tr>
                    <td>
                        <code class="text-primary">${item.batch_id.substring(0, 8)}...</code>
                    </td>
                    <td>${createdAt}</td>
                    <td>${taskInfo}</td>
                    <td>
                        <span class="text-muted" title="${item.retry_reason || ''}">
                            ${(item.retry_reason || '').substring(0, 30)}${(item.retry_reason || '').length > 30 ? '...' : ''}
                        </span>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-outline-info" onclick="showDetail('${item.id}')">
                            <i class="fas fa-eye me-1"></i>详情
                        </button>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    }
    
    // 获取状态徽章
    function getStatusBadge(status) {
        switch (status) {
            case 'success':
                return '<span class="badge bg-success">成功</span>';
            case 'failed':
                return '<span class="badge bg-danger">失败</span>';
            case 'pending':
                return '<span class="badge bg-warning">待处理</span>';
            default:
                return '<span class="badge bg-secondary">未知</span>';
        }
    }
    
    // 显示详情
    window.showDetail = function(id) {
        const item = currentData.find(d => d.id == id);
        if (!item) {
            alert('找不到记录详情');
            return;
        }
        
        let detailHtml = `
            <div class="row">
                <div class="col-md-6">
                    <h6>基本信息</h6>
                    <table class="table table-sm">
                        <tr><td>批次ID:</td><td><code>${item.batch_id}</code></td></tr>
                        <tr><td>状态:</td><td>${getStatusBadge(item.retry_status)}</td></tr>
                        <tr><td>创建时间:</td><td>${new Date(item.created_at).toLocaleString('zh-CN')}</td></tr>
                        <tr><td>更新时间:</td><td>${item.updated_at ? new Date(item.updated_at).toLocaleString('zh-CN') : '-'}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>重试原因</h6>
                    <p class="text-muted">${item.retry_reason || '无'}</p>
                </div>
            </div>
        `;
        
        if (item.original_task_data) {
            detailHtml += `
                <div class="mt-3">
                    <h6>原始任务数据</h6>
                    <pre class="bg-light p-2 rounded"><code>${JSON.stringify(JSON.parse(item.original_task_data), null, 2)}</code></pre>
                </div>
            `;
        }
        
        if (item.retry_result) {
            detailHtml += `
                <div class="mt-3">
                    <h6>重试结果</h6>
                    <pre class="bg-light p-2 rounded"><code>${item.retry_result}</code></pre>
                </div>
            `;
        }
        
        $('#detailContent').html(detailHtml);
        $('#detailModal').modal('show');
    };
    
    // 搜索按钮事件
    $('#searchBtn').on('click', function() {
        const batchId = $('#batchIdFilter').val().trim();
        const status = $('#statusFilter').val();
        loadRetryHistory(batchId, status);
    });
    
    // 刷新按钮事件
    $('#refreshBtn').on('click', function() {
        $('#batchIdFilter').val('');
        $('#statusFilter').val('');
        loadRetryHistory();
    });
    
    // 页面加载时执行
    loadRetryHistory();
});
</script>
{% endblock %}
