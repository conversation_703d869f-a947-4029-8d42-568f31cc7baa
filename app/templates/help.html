{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-5">
            <h1 class="display-6">
                <i class="fas fa-question-circle text-primary me-2"></i>
                使用帮助
            </h1>
            <p class="lead text-muted">详细的使用说明和常见问题解答</p>
        </div>

        <!-- 快速开始 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-rocket me-2"></i>
                    快速开始
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>第一步：准备Excel文件</h6>
                        <ul>
                            <li>下载Excel模板文件</li>
                            <li>按照模板格式填写任务信息</li>
                            <li>确保所有必填字段都已填写</li>
                            <li>配置姓名映射（可选）</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>第二步：上传和预览</h6>
                        <ul>
                            <li>在首页上传Excel文件</li>
                            <li>系统自动解析文件内容</li>
                            <li>在预览页面确认任务信息</li>
                            <li>查看任务层次结构</li>
                        </ul>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>第三步：配置Jira连接</h6>
                        <ul>
                            <li>选择环境（测试/生产）</li>
                            <li>输入用户名和Bearer Token</li>
                            <li>填写项目Key和Sprint名称</li>
                            <li>测试连接是否成功</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>第四步：批量创建任务</h6>
                        <ul>
                            <li>选择要创建的任务</li>
                            <li>按层次结构创建任务</li>
                            <li>查看创建结果和任务链接</li>
                            <li>重试失败的任务（如需要）</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Excel文件格式要求 -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-excel me-2"></i>
                    Excel文件格式要求
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    请严格按照以下格式填写Excel文件，列名必须完全一致
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>列名</th>
                                <th>是否必填</th>
                                <th>说明</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>橙卡</strong></td>
                                <td><span class="badge bg-warning">可选</span></td>
                                <td>需求的JIRA Key，用于关联父任务</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td><strong>模块</strong></td>
                                <td><span class="badge bg-warning">可选</span></td>
                                <td>任务所属的功能模块</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td><strong>主任务</strong></td>
                                <td><span class="badge bg-danger">必填</span></td>
                                <td>主要任务的描述（支持空值自动填充）</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td><strong>子任务</strong></td>
                                <td><span class="badge bg-danger">必填</span></td>
                                <td>具体的子任务描述</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td><strong>任务类型</strong></td>
                                <td><span class="badge bg-danger">必填</span></td>
                                <td>任务的类型分类</td>
                                <td>UI、API、数据、测试</td>
                            </tr>
                            <tr>
                                <td><strong>负责人</strong></td>
                                <td><span class="badge bg-danger">必填</span></td>
                                <td>任务的负责人（中文姓名）</td>
                                <td>张三、李四，请注意在姓名映射管理中配置对应的OA账号</td>
                            </tr>
                            <tr>
                                <td><strong>工作量</strong></td>
                                <td><span class="badge bg-warning">可选</span></td>
                                <td>预估的工作量（天）</td>
                                <td>0.3、1、2</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Jira配置说明 -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    Jira配置说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>环境选择</h6>
                        <p>系统支持多环境配置：</p>
                        <ul>
                            <li><strong>测试环境</strong>：用于开发和测试</li>
                            <li><strong>生产环境</strong>：正式生产环境</li>
                        </ul>
                        
                        <h6 class="mt-3">用户名</h6>
                        <p>您的Jira账户用户名，例如：</p>
                        <code>lidezheng</code>
                    </div>
                    <div class="col-md-6">
                        <h6>Bearer Token</h6>
                        <p>Jira API访问令牌，获取步骤：</p>
                        <ol>
                            <li>登录Jira系统</li>
                            <li>进入个人设置</li>
                            <li>创建API Token</li>
                            <li>复制生成的Token</li>
                        </ol>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>注意：</strong>Token是敏感信息，请妥善保管
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>项目Key</h6>
                        <p>Jira项目的Key，例如：</p>
                        <code>JGKEZH</code>
                    </div>
                    <div class="col-md-6">
                        <h6>Sprint名称</h6>
                        <p>迭代名称，例如：</p>
                        <code>INST2025-sprint10</code>
                    </div>
                </div>
            </div>
        </div>

        <!-- 姓名映射管理 -->
        <div class="card mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    姓名映射管理
                </h5>
            </div>
            <div class="card-body">
                <p>系统支持将中文姓名自动转换为OA账号，提高任务分配的准确性。</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6>功能说明：</h6>
                        <ul>
                            <li>在Excel中使用中文姓名</li>
                            <li>系统自动查询对应的OA账号</li>
                            <li>支持姓名映射的增删改查</li>
                            <li>支持模糊搜索</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>使用方法：</h6>
                        <ol>
                            <li>访问<a href="/name-mapping">姓名映射管理</a>页面</li>
                            <li>添加姓名到OA账号的映射</li>
                            <li>在Excel中使用中文姓名</li>
                            <li>系统自动完成转换</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- 常见问题 -->
        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    常见问题
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button collapsed" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#collapse1">
                                Q: 上传Excel文件时提示格式错误怎么办？
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>请检查以下几点：</p>
                                <ul>
                                    <li>确保文件格式为.xlsx或.xls</li>
                                    <li>检查是否包含所有必填列：主任务、子任务、任务类型、负责人</li>
                                    <li>确认列名与模板完全一致（区分大小写）</li>
                                    <li>检查是否有空行或格式异常</li>
                                    <li>确保任务类型只能是：UI、API、数据、测试</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#collapse2">
                                Q: Jira连接测试失败怎么办？
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>请检查以下配置：</p>
                                <ul>
                                    <li>确认选择了正确的环境（测试/生产）</li>
                                    <li>检查用户名和Bearer Token是否有效</li>
                                    <li>确认项目Key存在且有权限访问</li>
                                    <li>检查网络连接是否正常</li>
                                    <li>验证Token格式是否正确</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq3">
                            <button class="accordion-button collapsed" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#collapse3">
                                Q: 部分任务创建失败怎么办？
                            </button>
                        </h2>
                        <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>可以尝试以下解决方案：</p>
                                <ul>
                                    <li>查看失败原因的详细错误信息</li>
                                    <li>检查任务信息是否完整和正确</li>
                                    <li>确认负责人在姓名映射中存在</li>
                                    <li>检查主任务是否创建成功（子任务依赖主任务）</li>
                                    <li>验证Sprint名称是否正确</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
