{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-4">
            <h1 class="display-6">
                <i class="fas fa-upload text-primary me-2"></i>
                批量创建Jira任务
            </h1>
            <p class="lead text-muted">上传Excel文件，一键创建多个Jira任务</p>
        </div>

        <!-- 步骤指示器 -->
        <div class="row mb-4">
            <div class="col-md-4 text-center">
                <div class="step-indicator active">
                    <div class="step-number">1</div>
                    <div class="step-title">上传文件</div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="step-indicator">
                    <div class="step-number">2</div>
                    <div class="step-title">预览任务</div>
                </div>
            </div>
            <div class="col-md-4 text-center">
                <div class="step-indicator">
                    <div class="step-number">3</div>
                    <div class="step-title">创建完成</div>
                </div>
            </div>
        </div>

        <!-- 文件上传表单 -->
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-excel me-2"></i>
                    步骤1: 上传Excel文件
                </h5>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="excelFile" class="form-label">选择Excel文件</label>
                        <input type="file" class="form-control" id="excelFile" name="file" 
                               accept=".xlsx,.xls" required>
                        <div class="form-text">
                            支持.xlsx和.xls格式，文件大小不超过10MB
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sheetName" class="form-label">工作表名称（可选）</label>
                        <input type="text" class="form-control" id="sheetName" name="sheet_name" 
                               placeholder="留空则使用第一个工作表">
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            解析文件
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 进度条 -->
        <div id="progressContainer" class="mt-3" style="display: none;">
            <div class="progress">
                <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                     role="progressbar" style="width: 0%">
                    <span id="progressText">0%</span>
                </div>
            </div>
        </div>

        <!-- 结果显示区域 -->
        <div id="resultContainer" class="mt-4" style="display: none;">
            <!-- 结果内容将通过JavaScript动态填充 -->
        </div>

        <!-- 帮助信息 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    使用说明
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Excel文件格式要求：</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-1"></i> 包含以下列：橙卡、模块、主任务、子任务、任务类型、负责人、工作量、迭代</li>
                            <li><i class="fas fa-check text-success me-1"></i> 橙卡：需求的JIRA Key（如JGKEZH-23876）</li>
                            <li><i class="fas fa-check text-success me-1"></i> 任务类型：UI、API、测试</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>操作流程：</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-arrow-right text-primary me-1"></i> 上传Excel文件</li>
                            <li><i class="fas fa-arrow-right text-primary me-1"></i> 预览解析结果</li>
                            <li><i class="fas fa-arrow-right text-primary me-1"></i> 配置Jira连接</li>
                            <li><i class="fas fa-arrow-right text-primary me-1"></i> 批量创建任务</li>
                        </ul>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <a href="/template" class="btn btn-outline-primary">
                        <i class="fas fa-download me-1"></i>
                        下载Excel模板
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // 文件上传表单处理
    $('#uploadForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const file = $('#excelFile')[0].files[0];
        
        if (!file) {
            alert('请选择Excel文件');
            return;
        }
        
        // 显示进度条
        $('#progressContainer').show();
        $('#progressBar').css('width', '0%');
        $('#progressText').text('0%');
        
        // 模拟进度更新
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 10;
            $('#progressBar').css('width', progress + '%');
            $('#progressText').text(progress + '%');
            
            if (progress >= 90) {
                clearInterval(progressInterval);
            }
        }, 200);
        
        // 发送请求
        $.ajax({
            url: '/api/v1/parse-excel',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                clearInterval(progressInterval);
                $('#progressBar').css('width', '100%');
                $('#progressText').text('100%');
                
                if (response.success) {
                    // 存储解析结果到sessionStorage - 只存储任务数组
                    sessionStorage.setItem('parsedTasks', JSON.stringify(response.data.tasks));
                    
                    // 跳转到预览页面
                    setTimeout(() => {
                        window.location.href = '/preview';
                    }, 500);
                } else {
                    showError(response.message || '解析失败');
                }
            },
            error: function(xhr) {
                clearInterval(progressInterval);
                $('#progressContainer').hide();
                
                let errorMessage = '服务器错误';
                if (xhr.responseJSON && xhr.responseJSON.detail) {
                    errorMessage = xhr.responseJSON.detail;
                }
                showError(errorMessage);
            }
        });
    });
    
    function showError(message) {
        $('#resultContainer').html(`
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `).show();
    }
});
</script>
{% endblock %} 