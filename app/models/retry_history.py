#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: retry_history.py
描述: 重试历史记录模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
from typing import Optional, Dict, Any
import json

Base = declarative_base()

class RetryHistory(Base):
    """重试历史记录表"""
    __tablename__ = 'retry_history'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    batch_id = Column(String(50), nullable=False, comment='批次ID，用于关联同一次重试操作')
    original_task_data = Column(Text, comment='原始失败任务数据(JSON)')
    retry_task_data = Column(Text, comment='重试任务数据(JSON)')
    retry_reason = Column(String(500), comment='重试原因')
    retry_status = Column(String(20), default='pending', comment='重试状态: pending, success, failed')
    retry_result = Column(Text, comment='重试结果(JSON)')
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'batch_id': self.batch_id,
            'original_task_data': json.loads(self.original_task_data) if self.original_task_data else None,
            'retry_task_data': json.loads(self.retry_task_data) if self.retry_task_data else None,
            'retry_reason': self.retry_reason,
            'retry_status': self.retry_status,
            'retry_result': json.loads(self.retry_result) if self.retry_result else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def create_retry_record(cls, batch_id: str, original_task: Dict[str, Any], 
                           retry_task: Dict[str, Any], retry_reason: str = '') -> 'RetryHistory':
        """创建重试记录"""
        return cls(
            batch_id=batch_id,
            original_task_data=json.dumps(original_task, ensure_ascii=False),
            retry_task_data=json.dumps(retry_task, ensure_ascii=False),
            retry_reason=retry_reason,
            retry_status='pending'
        )
    
    def update_retry_result(self, status: str, result: Dict[str, Any]):
        """更新重试结果"""
        self.retry_status = status
        self.retry_result = json.dumps(result, ensure_ascii=False)
        self.updated_at = datetime.now()


# 创建表的SQL语句
CREATE_TABLE_SQL = """
CREATE TABLE IF NOT EXISTS retry_history (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    batch_id VARCHAR(50) NOT NULL COMMENT '批次ID，用于关联同一次重试操作',
    original_task_data TEXT COMMENT '原始失败任务数据(JSON)',
    retry_task_data TEXT COMMENT '重试任务数据(JSON)',
    retry_reason VARCHAR(500) COMMENT '重试原因',
    retry_status VARCHAR(20) DEFAULT 'pending' COMMENT '重试状态: pending, success, failed',
    retry_result TEXT COMMENT '重试结果(JSON)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_batch_id (batch_id),
    INDEX idx_retry_status (retry_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='重试历史记录表';
"""
