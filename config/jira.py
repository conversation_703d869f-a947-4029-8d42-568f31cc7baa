#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: jira.py
描述: JIRA环境配置文件

更新日志:
2024-01-15 - 创建JIRA环境配置模块
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class JiraConfig:
    """JIRA环境配置类"""
    
    def __init__(self):
        # JIRA环境配置
        self.environments = {
            'test': {
                'name': '测试环境',
                'url': os.getenv('JIRA_TEST_URL', 'http://jirauat.gf.com.cn'),
                'description': '用于开发和测试'
            },
            'production': {
                'name': '生产环境', 
                'url': os.getenv('JIRA_PROD_URL', 'http://jira.gf.com.cn'),
                'description': '正式生产环境'
            }
        }
        
        # 默认环境
        self.default_environment = os.getenv('JIRA_DEFAULT_ENV', 'test')
    
    def get_environment(self, env_key: str) -> Dict[str, Any]:
        """获取指定环境配置"""
        return self.environments.get(env_key, self.environments[self.default_environment])
    
    def get_all_environments(self) -> Dict[str, Dict[str, Any]]:
        """获取所有环境配置"""
        return self.environments
    
    def get_environment_url(self, env_key: str) -> str:
        """获取指定环境的URL"""
        env_config = self.get_environment(env_key)
        return env_config.get('url', '')

# 全局配置实例
jira_config = JiraConfig()
