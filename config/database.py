#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名: database.py
描述: 数据库配置文件

更新日志:
2024-01-15 - 创建数据库配置模块
"""

import os
from typing import Optional

class DatabaseConfig:
    """数据库配置类"""
    
    def __init__(self):
        # 从环境变量读取配置，如果没有则使用默认值
        self.host = os.getenv('DB_HOST', '************')
        self.port = int(os.getenv('DB_PORT', '15020'))
        self.username = os.getenv('DB_USERNAME', 'srms')
        self.password = os.getenv('DB_PASSWORD', 'Srms_12345678')
        self.database = os.getenv('DB_DATABASE', 'srms')
        self.charset = os.getenv('DB_CHARSET', 'utf8mb4')
        
        # 连接池配置
        self.pool_size = int(os.getenv('DB_POOL_SIZE', '10'))
        self.max_overflow = int(os.getenv('DB_MAX_OVERFLOW', '20'))
        self.pool_timeout = int(os.getenv('DB_POOL_TIMEOUT', '30'))
        self.pool_recycle = int(os.getenv('DB_POOL_RECYCLE', '3600'))
    
    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
    
    def get_connection_params(self) -> dict:
        """获取连接参数字典"""
        return {
            'host': self.host,
            'port': self.port,
            'user': self.username,
            'password': self.password,
            'database': self.database,
            'charset': self.charset,
            'autocommit': True,
            'connect_timeout': 10,
            'read_timeout': 30,
            'write_timeout': 30
        }

# 全局配置实例
db_config = DatabaseConfig()
