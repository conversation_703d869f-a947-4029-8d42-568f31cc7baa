2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:322] - ================================================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:322] - ================================================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:323] - 开始按层次结构批量创建JIRA任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:323] - 开始按层次结构批量创建JIRA任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:324] - 任务数量: 11
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:324] - 任务数量: 11
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:325] - 环境: test
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:325] - 环境: test
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:326] - 项目: JGKEZH
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:326] - 项目: JGKEZH
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:327] - 用户: lidezheng
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:327] - 用户: lidezheng
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:328] - Sprint: BSP2022-sprint9
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:328] - Sprint: BSP2022-sprint9
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:329] - ================================================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:329] - ================================================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:396] - 开始分析任务层次结构...
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:396] - 开始分析任务层次结构...
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS图片裁剪功能优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS图片裁剪功能优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS代办通知邮件
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS代办通知邮件
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:433] - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:433] - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:334] - 分析结果: 5 个主任务, 11 个子任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:334] - 分析结果: 5 个主任务, 11 个子任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:445] - ============================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:445] - ============================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:446] - 步骤1: 开始创建主任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:446] - 步骤1: 开始创建主任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:447] - 需要创建 5 个主任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:447] - 需要创建 5 个主任务
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:448] - ============================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:448] - ============================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS图片裁剪功能优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS图片裁剪功能优化
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:39 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:41 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535640x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=379F00556C36D403CFDCABA7E13DB998; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_8cd1e77e9ab14eb585f640e3c5bf258270ba616a_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'ufs6ys', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:41 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535640x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=379F00556C36D403CFDCABA7E13DB998; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_8cd1e77e9ab14eb585f640e3c5bf258270ba616a_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'ufs6ys', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857155",
  "key": "JGKEZH-12524",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857155"
}
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857155",
  "key": "JGKEZH-12524",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857155"
}
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12524
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12524
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS图片裁剪功能优化 -> JGKEZH-12524
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS图片裁剪功能优化 -> JGKEZH-12524
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS代办通知邮件
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS代办通知邮件
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:40 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:41 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535642x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=3BF7498AD5A6B8337192BAA7FC041D45; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_063ee950d0ddb640d1091bf13ca7cf76795f10ff_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'xonsvi', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:41 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535642x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=3BF7498AD5A6B8337192BAA7FC041D45; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_063ee950d0ddb640d1091bf13ca7cf76795f10ff_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'xonsvi', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857156",
  "key": "JGKEZH-12525",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857156"
}
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857156",
  "key": "JGKEZH-12525",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857156"
}
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12525
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12525
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS代办通知邮件 -> JGKEZH-12525
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS代办通知邮件 -> JGKEZH-12525
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:452] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:452] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:41 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:42 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535643x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=CBA5C1AEEB3A995022677276D8B603D5; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_7c55dad2393e682e55fc585a9f7ac5a79fc79cac_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '717hff', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:42 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535643x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=CBA5C1AEEB3A995022677276D8B603D5; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_7c55dad2393e682e55fc585a9f7ac5a79fc79cac_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '717hff', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857157",
  "key": "JGKEZH-12526",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857157"
}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857157",
  "key": "JGKEZH-12526",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857157"
}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12526
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12526
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: PB系统支持退回删除操作员申请 -> JGKEZH-12526
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: PB系统支持退回删除操作员申请 -> JGKEZH-12526
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS端「产品目录树」
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS端「产品目录树」
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:43 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535644x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=28F6CD43E3FDB724FC9AEE0EBD12D612; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_ccf42562306a6eadeb2443232ee3375b9e7e4665_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '4k3h9q', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:43 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535644x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=28F6CD43E3FDB724FC9AEE0EBD12D612; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_ccf42562306a6eadeb2443232ee3375b9e7e4665_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '4k3h9q', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857158",
  "key": "JGKEZH-12527",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857158"
}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857158",
  "key": "JGKEZH-12527",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857158"
}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12527
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12527
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS端「产品目录树」 -> JGKEZH-12527
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS端「产品目录树」 -> JGKEZH-12527
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:42 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:43 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535645x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=E6D86AF91C7925AF348A7679613E6A37; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_7b196f4447bc3b7e5d421d4ad9a4c2bfcca82ba6_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1hf9vx0', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:43 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535645x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=E6D86AF91C7925AF348A7679613E6A37; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_7b196f4447bc3b7e5d421d4ad9a4c2bfcca82ba6_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1hf9vx0', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857159",
  "key": "JGKEZH-12528",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857159"
}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857159",
  "key": "JGKEZH-12528",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857159"
}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12528
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12528
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS「产品管理-开通列表」优化 -> JGKEZH-12528
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS「产品管理-开通列表」优化 -> JGKEZH-12528
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:501] - 主任务创建完成: 成功 5 个，失败 0 个
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:501] - 主任务创建完成: 成功 5 个，失败 0 个
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:640] - ============================================================
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:640] - ============================================================
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:641] - 步骤2: 开始Sprint关联
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:641] - 步骤2: 开始Sprint关联
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:642] - Sprint名称: BSP2022-sprint9
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:642] - Sprint名称: BSP2022-sprint9
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:643] - 需要关联 5 个主任务
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:643] - 需要关联 5 个主任务
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:644] - ============================================================
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:644] - ============================================================
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:715] - 查询Sprint ID: http://jirauat.gf.com.cn/rest/greenhopper/1.0/sprint/picker?query=BSP2022-sprint9
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:715] - 查询Sprint ID: http://jirauat.gf.com.cn/rest/greenhopper/1.0/sprint/picker?query=BSP2022-sprint9
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:720] - Sprint查询请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Accept': 'application/json'}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:720] - Sprint查询请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Accept': 'application/json'}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:724] - Sprint查询响应状态码: 200
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:724] - Sprint查询响应状态码: 200
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:725] - Sprint查询响应: {"suggestions":[{"name":"BSP2022-sprint9","id":6514,"stateKey":"ACTIVE","boardName":"机构客户APP-Scrum","date":"2022-05-05T09:12:27Z"}],"allMatches":[]}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:725] - Sprint查询响应: {"suggestions":[{"name":"BSP2022-sprint9","id":6514,"stateKey":"ACTIVE","boardName":"机构客户APP-Scrum","date":"2022-05-05T09:12:27Z"}],"allMatches":[]}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:733] - 从suggestions找到Sprint ID: 6514
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:733] - 从suggestions找到Sprint ID: 6514
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:651] - 找到Sprint ID: 6514
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:651] - 找到Sprint ID: 6514
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12524 到Sprint 6514
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12524 到Sprint 6514
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12524
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12524
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:43 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12524 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12524 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12525 到Sprint 6514
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12525 到Sprint 6514
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12525
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12525
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12525 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12525 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12526 到Sprint 6514
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12526 到Sprint 6514
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12526
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12526
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12526 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12526 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12527 到Sprint 6514
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12527 到Sprint 6514
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12527
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12527
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:44 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12527 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12527 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12528 到Sprint 6514
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12528 到Sprint 6514
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12528
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12528
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12528 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12528 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:699] - Sprint关联完成: 成功 5 个
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:699] - Sprint关联完成: 成功 5 个
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:805] - ============================================================
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:805] - ============================================================
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:806] - 步骤3: 开始需求关联
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:806] - 步骤3: 开始需求关联
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:807] - ============================================================
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:807] - ============================================================
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12524
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12524
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12524
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12524
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12524
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12524
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12524
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12524
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12525
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12525
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12525
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12525
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12525
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12525
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12525
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12525
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12526
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12526
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12526
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12526
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12526
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12526
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:45 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12526
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12526
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12527
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12527
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12527
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12527
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12527
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12527
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12527
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12527
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12528
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12528
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12528
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12528
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12528
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12528
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12528
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12528
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:839] - 需求关联完成: 成功 5 个
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:839] - 需求关联完成: 成功 5 个
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:907] - ============================================================
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:907] - ============================================================
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:908] - 步骤4: 开始创建子任务
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:908] - 步骤4: 开始创建子任务
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:909] - 需要创建 11 个子任务
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:909] - 需要创建 11 个子任务
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:910] - 成功创建的主任务数量: 5
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:910] - 成功创建的主任务数量: 5
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:911] - ============================================================
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:911] - ============================================================
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:954] - 有效子任务: 11 个
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:954] - 有效子任务: 11 个
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:955] - 因主任务失败而跳过的子任务: 0 个
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:955] - 因主任务失败而跳过的子任务: 0 个
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:964] - 创建子任务 1/11: 多个场景设定图片上传比例 -> 主任务: JGKEZH-12524
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:964] - 创建子任务 1/11: 多个场景设定图片上传比例 -> 主任务: JGKEZH-12524
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12524"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12524"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:46 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:47 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535658x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=68201081D4D99EDAC11230DCE2A24509; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_036efe33aa0caf81d0ae258fd6557d89d000eaef_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1wumfbp', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:47 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535658x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=68201081D4D99EDAC11230DCE2A24509; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_036efe33aa0caf81d0ae258fd6557d89d000eaef_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1wumfbp', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857160",
  "key": "JGKEZH-12529",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857160"
}
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857160",
  "key": "JGKEZH-12529",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857160"
}
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12529
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12529
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 多个场景设定图片上传比例 -> JGKEZH-12529
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 多个场景设定图片上传比例 -> JGKEZH-12529
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:964] - 创建子任务 2/11: 发送邮件模板通知调整 -> 主任务: JGKEZH-12525
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:964] - 创建子任务 2/11: 发送邮件模板通知调整 -> 主任务: JGKEZH-12525
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "发送邮件模板通知调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12525"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "发送邮件模板通知调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12525"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:47 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:48 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535659x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=93162D1BFB7F67802EA14C86359EDC34; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_9140bfc93352d4dc314a47a77070c2fe824e7cb3_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'hrux3t', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:48 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535659x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=93162D1BFB7F67802EA14C86359EDC34; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_9140bfc93352d4dc314a47a77070c2fe824e7cb3_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'hrux3t', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857161",
  "key": "JGKEZH-12530",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857161"
}
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857161",
  "key": "JGKEZH-12530",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857161"
}
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12530
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12530
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 发送邮件模板通知调整 -> JGKEZH-12530
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 发送邮件模板通知调整 -> JGKEZH-12530
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:964] - 创建子任务 3/11: cms审核支持退回 -> 主任务: JGKEZH-12526
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:964] - 创建子任务 3/11: cms审核支持退回 -> 主任务: JGKEZH-12526
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "cms审核支持退回",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12526"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.1d",
      "remainingEstimate": "0.1d"
    }
  }
}
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "cms审核支持退回",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12526"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.1d",
      "remainingEstimate": "0.1d"
    }
  }
}
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:48 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:49 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535660x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=12E1F6B65FEEACE555C5A82ECF2F2BE8; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_f004422c85e15c14742dd53daec24cff49b3c297_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '13xztl', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:49 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535660x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=12E1F6B65FEEACE555C5A82ECF2F2BE8; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_f004422c85e15c14742dd53daec24cff49b3c297_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '13xztl', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857162",
  "key": "JGKEZH-12531",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857162"
}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857162",
  "key": "JGKEZH-12531",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857162"
}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12531
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12531
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: cms审核支持退回 -> JGKEZH-12531
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: cms审核支持退回 -> JGKEZH-12531
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:964] - 创建子任务 4/11: web删除操作员逻辑调整 -> 主任务: JGKEZH-12526
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:964] - 创建子任务 4/11: web删除操作员逻辑调整 -> 主任务: JGKEZH-12526
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "web删除操作员逻辑调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12526"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "web删除操作员逻辑调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12526"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:50 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535661x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=CDD1B10FA0F654EB7075D6214C668549; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_2efa2fa6bad2ab2c9bc0c85db93209b8c518256d_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1x7t5bl', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:50 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535661x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=CDD1B10FA0F654EB7075D6214C668549; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_2efa2fa6bad2ab2c9bc0c85db93209b8c518256d_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1x7t5bl', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857163",
  "key": "JGKEZH-12532",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857163"
}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857163",
  "key": "JGKEZH-12532",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857163"
}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12532
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12532
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: web删除操作员逻辑调整 -> JGKEZH-12532
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: web删除操作员逻辑调整 -> JGKEZH-12532
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:964] - 创建子任务 5/11: CMS删除操作员逻辑调整 -> 主任务: JGKEZH-12526
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:964] - 创建子任务 5/11: CMS删除操作员逻辑调整 -> 主任务: JGKEZH-12526
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS删除操作员逻辑调整",
    "issuetype": {
      "id": "11015"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12526"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS删除操作员逻辑调整",
    "issuetype": {
      "id": "11015"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12526"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:49 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:50 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535662x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1FAA416588F75CC2EB76D771CCE5B020; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_4479834fb2950d4bc68e53afa6db5ad296ffb6fc_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '12v0kdb', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:50 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535662x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1FAA416588F75CC2EB76D771CCE5B020; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_4479834fb2950d4bc68e53afa6db5ad296ffb6fc_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '12v0kdb', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857164",
  "key": "JGKEZH-12533",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857164"
}
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857164",
  "key": "JGKEZH-12533",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857164"
}
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12533
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12533
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: CMS删除操作员逻辑调整 -> JGKEZH-12533
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: CMS删除操作员逻辑调整 -> JGKEZH-12533
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:964] - 创建子任务 6/11: 产品目录树列表和筛选 -> 主任务: JGKEZH-12527
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:964] - 创建子任务 6/11: 产品目录树列表和筛选 -> 主任务: JGKEZH-12527
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表和筛选",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12527"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表和筛选",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12527"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:50 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:51 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535663x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=DBEB565E49ED2C4CF383E6D454A0F3FC; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d6be0d7414eae6762152c92d6901684f5a44b98c_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1gr1hcg', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:51 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535663x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=DBEB565E49ED2C4CF383E6D454A0F3FC; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d6be0d7414eae6762152c92d6901684f5a44b98c_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1gr1hcg', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857165",
  "key": "JGKEZH-12534",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857165"
}
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857165",
  "key": "JGKEZH-12534",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857165"
}
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12534
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12534
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品目录树列表和筛选 -> JGKEZH-12534
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品目录树列表和筛选 -> JGKEZH-12534
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:964] - 创建子任务 7/11: 产品管理新增【关联产品目录树】选项 -> 主任务: JGKEZH-12527
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:964] - 创建子任务 7/11: 产品管理新增【关联产品目录树】选项 -> 主任务: JGKEZH-12527
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品管理新增【关联产品目录树】选项",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12527"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品管理新增【关联产品目录树】选项",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12527"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:51 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:52 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535664x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=F391D6C48CE23EEE9D79339360F42F41; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_9b88d67e1c641be44ba2da24e6cc4f02fb41b6f3_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '9vd6re', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:52 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535664x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=F391D6C48CE23EEE9D79339360F42F41; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_9b88d67e1c641be44ba2da24e6cc4f02fb41b6f3_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '9vd6re', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857166",
  "key": "JGKEZH-12535",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857166"
}
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857166",
  "key": "JGKEZH-12535",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857166"
}
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12535
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12535
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品管理新增【关联产品目录树】选项 -> JGKEZH-12535
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品管理新增【关联产品目录树】选项 -> JGKEZH-12535
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:964] - 创建子任务 8/11: 产品目录树列表 -> 主任务: JGKEZH-12527
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:964] - 创建子任务 8/11: 产品目录树列表 -> 主任务: JGKEZH-12527
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12527"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12527"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:52 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:53 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535666x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1C0AEB308C9FA96CC7BAFDA66B9CEB10; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_b0ed522d2e27b3b68b32a5def7fb5a2a98d1be77_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1ufwj1f', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:53 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535666x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1C0AEB308C9FA96CC7BAFDA66B9CEB10; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_b0ed522d2e27b3b68b32a5def7fb5a2a98d1be77_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1ufwj1f', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857167",
  "key": "JGKEZH-12536",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857167"
}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857167",
  "key": "JGKEZH-12536",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857167"
}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12536
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12536
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品目录树列表 -> JGKEZH-12536
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品目录树列表 -> JGKEZH-12536
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:964] - 创建子任务 9/11: 目录新增&编辑 -> 主任务: JGKEZH-12527
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:964] - 创建子任务 9/11: 目录新增&编辑 -> 主任务: JGKEZH-12527
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "目录新增&编辑",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12527"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "目录新增&编辑",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12527"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:54 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535667x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=02B48E5FF5F6EE85B31FD07F8F2E2BD3; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_32e5a3d5956246f7d97af3ebe24f0db5250a14bb_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'lmscyr', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:54 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535667x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=02B48E5FF5F6EE85B31FD07F8F2E2BD3; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_32e5a3d5956246f7d97af3ebe24f0db5250a14bb_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'lmscyr', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857168",
  "key": "JGKEZH-12537",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857168"
}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857168",
  "key": "JGKEZH-12537",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857168"
}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12537
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12537
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 目录新增&编辑 -> JGKEZH-12537
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 目录新增&编辑 -> JGKEZH-12537
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:964] - 创建子任务 10/11: 筛选项新增 类型 -> 主任务: JGKEZH-12528
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:964] - 创建子任务 10/11: 筛选项新增 类型 -> 主任务: JGKEZH-12528
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "筛选项新增 类型",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12528"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.2d",
      "remainingEstimate": "0.2d"
    }
  }
}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "筛选项新增 类型",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12528"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.2d",
      "remainingEstimate": "0.2d"
    }
  }
}
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:53 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:54 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535668x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=9E6A2122282DB0DFEE9EEBEA34FDABF3; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_59888fc5ab84ac88dbac5626a80f864f94b67000_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1fc5ml6', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:54 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535668x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=9E6A2122282DB0DFEE9EEBEA34FDABF3; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_59888fc5ab84ac88dbac5626a80f864f94b67000_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1fc5ml6', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857169",
  "key": "JGKEZH-12538",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857169"
}
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857169",
  "key": "JGKEZH-12538",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857169"
}
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12538
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12538
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 筛选项新增 类型 -> JGKEZH-12538
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 筛选项新增 类型 -> JGKEZH-12538
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:964] - 创建子任务 11/11: 开通列表支持类型筛选 -> 主任务: JGKEZH-12528
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:964] - 创建子任务 11/11: 开通列表支持类型筛选 -> 主任务: JGKEZH-12528
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "开通列表支持类型筛选",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12528"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "开通列表支持类型筛选",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12528"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:54 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:55 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535669x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=0A319D4C92425ADAA89D01FA9679EB8D; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_e7a7091862965e53fd3e7876a78e3a397aff4889_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1sm8vxg', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:46:55 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '828x535669x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=0A319D4C92425ADAA89D01FA9679EB8D; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_e7a7091862965e53fd3e7876a78e3a397aff4889_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1sm8vxg', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857170",
  "key": "JGKEZH-12539",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857170"
}
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857170",
  "key": "JGKEZH-12539",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857170"
}
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12539
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12539
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 开通列表支持类型筛选 -> JGKEZH-12539
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 开通列表支持类型筛选 -> JGKEZH-12539
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:1014] - 子任务创建完成: 成功 11 个，失败 0 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:1014] - 子任务创建完成: 成功 11 个，失败 0 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:373] - JIRA 批量任务创建完成:
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:373] - JIRA 批量任务创建完成:
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:374] - 总任务数: 11
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:374] - 总任务数: 11
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:375] - 主任务创建: 5 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:375] - 主任务创建: 5 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:376] - 子任务创建: 11 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:376] - 子任务创建: 11 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:377] - Sprint关联: 5 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:377] - Sprint关联: 5 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:378] - 需求关联: 5 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:378] - 需求关联: 5 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:379] - 成功: 16 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:379] - 成功: 16 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:380] - 失败: 0 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:380] - 失败: 0 个
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:381] - ================================================================================
2025-07-11 13:46:55 - jira_api - INFO - [jira_service.py:381] - ================================================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:322] - ================================================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:322] - ================================================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:323] - 开始按层次结构批量创建JIRA任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:323] - 开始按层次结构批量创建JIRA任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:324] - 任务数量: 11
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:324] - 任务数量: 11
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:325] - 环境: test
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:325] - 环境: test
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:326] - 项目: JGKEZH
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:326] - 项目: JGKEZH
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:327] - 用户: lidezheng
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:327] - 用户: lidezheng
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:328] - Sprint: BSP2022-sprint9
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:328] - Sprint: BSP2022-sprint9
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:329] - ================================================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:329] - ================================================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:396] - 开始分析任务层次结构...
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:396] - 开始分析任务层次结构...
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS图片裁剪功能优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS图片裁剪功能优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS代办通知邮件
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS代办通知邮件
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:415] - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:431] - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:433] - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:433] - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:334] - 分析结果: 5 个主任务, 11 个子任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:334] - 分析结果: 5 个主任务, 11 个子任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:445] - ============================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:445] - ============================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:446] - 步骤1: 开始创建主任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:446] - 步骤1: 开始创建主任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:447] - 需要创建 5 个主任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:447] - 需要创建 5 个主任务
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:448] - ============================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:448] - ============================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS图片裁剪功能优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS图片裁剪功能优化
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:26 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:27 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535687x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=C9970517FC64F96B42294F449E0D81EB; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_7fb3b3e32b007978ff3e90a2737ca4690565a84b_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1xuds5r', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:27 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535687x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=C9970517FC64F96B42294F449E0D81EB; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_7fb3b3e32b007978ff3e90a2737ca4690565a84b_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1xuds5r', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857171",
  "key": "JGKEZH-12540",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857171"
}
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857171",
  "key": "JGKEZH-12540",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857171"
}
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12540
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12540
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS图片裁剪功能优化 -> JGKEZH-12540
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS图片裁剪功能优化 -> JGKEZH-12540
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS代办通知邮件
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS代办通知邮件
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:27 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:28 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535688x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=A48189546A675E979C6E8C200229B0F0; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_7351160637c863f5038d219f477a2ca6e4e58a57_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'zx2x8p', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:28 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535688x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=A48189546A675E979C6E8C200229B0F0; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_7351160637c863f5038d219f477a2ca6e4e58a57_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'zx2x8p', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857172",
  "key": "JGKEZH-12541",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857172"
}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857172",
  "key": "JGKEZH-12541",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857172"
}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12541
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12541
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS代办通知邮件 -> JGKEZH-12541
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS代办通知邮件 -> JGKEZH-12541
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:452] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:452] - 创建主任务: PB系统支持退回删除操作员申请
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:29 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535689x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=AD500D089789474E77A5DB70DA76409D; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_c3322144fb8a42e3c4c189db782c9223d354388f_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'n93ec5', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:29 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535689x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=AD500D089789474E77A5DB70DA76409D; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_c3322144fb8a42e3c4c189db782c9223d354388f_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'n93ec5', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857173",
  "key": "JGKEZH-12542",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857173"
}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857173",
  "key": "JGKEZH-12542",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857173"
}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12542
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12542
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: PB系统支持退回删除操作员申请 -> JGKEZH-12542
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: PB系统支持退回删除操作员申请 -> JGKEZH-12542
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS端「产品目录树」
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS端「产品目录树」
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:28 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:29 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535690x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=6AF05B65EA077C7A00EF76246AE6A77E; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_4dcd64ece0f1462d81edf1fee355017f14c57e15_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'n263gx', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:29 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535690x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=6AF05B65EA077C7A00EF76246AE6A77E; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_4dcd64ece0f1462d81edf1fee355017f14c57e15_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'n263gx', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857174",
  "key": "JGKEZH-12543",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857174"
}
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857174",
  "key": "JGKEZH-12543",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857174"
}
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12543
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12543
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS端「产品目录树」 -> JGKEZH-12543
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS端「产品目录树」 -> JGKEZH-12543
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:452] - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建main_task请求详情:
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "lidezheng"
    }
  }
}
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:29 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建main_task响应详情:
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:30 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535691x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=375B46200C7DC7DA8D8C0A70781AA017; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_5cd616c69130c4a2480314becee174d1c2be21b4_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1sjy5jg', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:30 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535691x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=375B46200C7DC7DA8D8C0A70781AA017; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_5cd616c69130c4a2480314becee174d1c2be21b4_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1sjy5jg', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857175",
  "key": "JGKEZH-12544",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857175"
}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857175",
  "key": "JGKEZH-12544",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857175"
}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12544
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:611] - 成功创建main_task: JGKEZH-12544
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS「产品管理-开通列表」优化 -> JGKEZH-12544
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:476] - 主任务创建成功: CMS「产品管理-开通列表」优化 -> JGKEZH-12544
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:501] - 主任务创建完成: 成功 5 个，失败 0 个
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:501] - 主任务创建完成: 成功 5 个，失败 0 个
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:640] - ============================================================
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:640] - ============================================================
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:641] - 步骤2: 开始Sprint关联
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:641] - 步骤2: 开始Sprint关联
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:642] - Sprint名称: BSP2022-sprint9
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:642] - Sprint名称: BSP2022-sprint9
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:643] - 需要关联 5 个主任务
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:643] - 需要关联 5 个主任务
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:644] - ============================================================
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:644] - ============================================================
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:715] - 查询Sprint ID: http://jirauat.gf.com.cn/rest/greenhopper/1.0/sprint/picker?query=BSP2022-sprint9
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:715] - 查询Sprint ID: http://jirauat.gf.com.cn/rest/greenhopper/1.0/sprint/picker?query=BSP2022-sprint9
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:720] - Sprint查询请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Accept': 'application/json'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:720] - Sprint查询请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Accept': 'application/json'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:724] - Sprint查询响应状态码: 200
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:724] - Sprint查询响应状态码: 200
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:725] - Sprint查询响应: {"suggestions":[{"name":"BSP2022-sprint9","id":6514,"stateKey":"ACTIVE","boardName":"机构客户APP-Scrum","date":"2022-05-05T09:12:27Z"}],"allMatches":[]}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:725] - Sprint查询响应: {"suggestions":[{"name":"BSP2022-sprint9","id":6514,"stateKey":"ACTIVE","boardName":"机构客户APP-Scrum","date":"2022-05-05T09:12:27Z"}],"allMatches":[]}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:733] - 从suggestions找到Sprint ID: 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:733] - 从suggestions找到Sprint ID: 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:651] - 找到Sprint ID: 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:651] - 找到Sprint ID: 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12540 到Sprint 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12540 到Sprint 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12540
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12540
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12540 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12540 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12541 到Sprint 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12541 到Sprint 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12541
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12541
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12541 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12541 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12542 到Sprint 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12542 到Sprint 6514
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12542
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12542
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:30 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12542 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12542 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12543 到Sprint 6514
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12543 到Sprint 6514
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12543
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12543
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12543 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12543 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12544 到Sprint 6514
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:774] - 关联任务 JGKEZH-12544 到Sprint 6514
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12544
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:775] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12544
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:780] - Sprint关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:781] - 请求体: {
  "fields": {
    "customfield_10005": 6514
  }
}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:790] - Sprint关联响应状态码: 204
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:791] - Sprint关联响应: 
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12544 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:658] - 任务 JGKEZH-12544 成功关联到Sprint BSP2022-sprint9
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:699] - Sprint关联完成: 成功 5 个
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:699] - Sprint关联完成: 成功 5 个
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:805] - ============================================================
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:805] - ============================================================
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:806] - 步骤3: 开始需求关联
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:806] - 步骤3: 开始需求关联
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:807] - ============================================================
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:807] - ============================================================
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12540
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12540
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12540
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12540
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12540
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12540
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:31 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12540
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12540
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12541
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12541
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12541
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12541
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12541
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12541
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12541
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12541
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12542
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12542
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12542
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11433 到任务 JGKEZH-12542
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12542
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12542
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12542
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12542
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12543
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12543
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12543
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12543
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12543
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12543
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12543
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12543
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12544
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:819] - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12544
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12544
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:875] - 关联需求 JGKEZH-11521 到任务 JGKEZH-12544
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12544
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:876] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12544
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:881] - 需求关联请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:32 - jira_api - INFO - [jira_service.py:882] - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:891] - 需求关联响应状态码: 204
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:892] - 需求关联响应: 
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12544
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:822] - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12544
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:839] - 需求关联完成: 成功 5 个
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:839] - 需求关联完成: 成功 5 个
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:907] - ============================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:907] - ============================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:908] - 步骤4: 开始创建子任务
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:908] - 步骤4: 开始创建子任务
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:909] - 需要创建 11 个子任务
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:909] - 需要创建 11 个子任务
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:910] - 成功创建的主任务数量: 5
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:910] - 成功创建的主任务数量: 5
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:911] - ============================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:911] - ============================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:954] - 有效子任务: 11 个
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:954] - 有效子任务: 11 个
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:955] - 因主任务失败而跳过的子任务: 0 个
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:955] - 因主任务失败而跳过的子任务: 0 个
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:964] - 创建子任务 1/11: 多个场景设定图片上传比例 -> 主任务: JGKEZH-12540
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:964] - 创建子任务 1/11: 多个场景设定图片上传比例 -> 主任务: JGKEZH-12540
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12540"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12540"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:34 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535703x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=7B22D00B834DCC9F8C1766EE23D87635; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_c56ba2746097d7d7ebb90155f15b28c94fa58be3_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'ttu7pm', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:34 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535703x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=7B22D00B834DCC9F8C1766EE23D87635; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_c56ba2746097d7d7ebb90155f15b28c94fa58be3_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'ttu7pm', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857176",
  "key": "JGKEZH-12545",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857176"
}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857176",
  "key": "JGKEZH-12545",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857176"
}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12545
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12545
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 多个场景设定图片上传比例 -> JGKEZH-12545
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 多个场景设定图片上传比例 -> JGKEZH-12545
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:964] - 创建子任务 2/11: 发送邮件模板通知调整 -> 主任务: JGKEZH-12541
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:964] - 创建子任务 2/11: 发送邮件模板通知调整 -> 主任务: JGKEZH-12541
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "发送邮件模板通知调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12541"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "发送邮件模板通知调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12541"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:33 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:34 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535704x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=A14C353DDF6F2600EFE2E54CB3435D94; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_c3535a6f438739e85db029edef20ec245e86323f_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'md4u2', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:34 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535704x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=A14C353DDF6F2600EFE2E54CB3435D94; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_c3535a6f438739e85db029edef20ec245e86323f_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'md4u2', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857177",
  "key": "JGKEZH-12546",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857177"
}
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857177",
  "key": "JGKEZH-12546",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857177"
}
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12546
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12546
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 发送邮件模板通知调整 -> JGKEZH-12546
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 发送邮件模板通知调整 -> JGKEZH-12546
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:964] - 创建子任务 3/11: cms审核支持退回 -> 主任务: JGKEZH-12542
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:964] - 创建子任务 3/11: cms审核支持退回 -> 主任务: JGKEZH-12542
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "cms审核支持退回",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12542"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.1d",
      "remainingEstimate": "0.1d"
    }
  }
}
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "cms审核支持退回",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12542"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.1d",
      "remainingEstimate": "0.1d"
    }
  }
}
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:34 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:35 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535705x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=CA1335C480BC174C35C94E274BF870A5; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_fc86e4d5424afa2f9944d72352f8b60c7c720a99_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '166m46d', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:35 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535705x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=CA1335C480BC174C35C94E274BF870A5; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_fc86e4d5424afa2f9944d72352f8b60c7c720a99_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '166m46d', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857178",
  "key": "JGKEZH-12547",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857178"
}
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857178",
  "key": "JGKEZH-12547",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857178"
}
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12547
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12547
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: cms审核支持退回 -> JGKEZH-12547
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: cms审核支持退回 -> JGKEZH-12547
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:964] - 创建子任务 4/11: web删除操作员逻辑调整 -> 主任务: JGKEZH-12542
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:964] - 创建子任务 4/11: web删除操作员逻辑调整 -> 主任务: JGKEZH-12542
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "web删除操作员逻辑调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12542"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "web删除操作员逻辑调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12542"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:35 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:36 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535706x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=44EC8DA9249151365E2E8794C4AC5533; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_778fc0bf10f30f6d4c75a41fd82685a4e3784712_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '10abp19', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:36 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535706x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=44EC8DA9249151365E2E8794C4AC5533; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_778fc0bf10f30f6d4c75a41fd82685a4e3784712_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '10abp19', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857179",
  "key": "JGKEZH-12548",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857179"
}
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857179",
  "key": "JGKEZH-12548",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857179"
}
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12548
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12548
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: web删除操作员逻辑调整 -> JGKEZH-12548
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: web删除操作员逻辑调整 -> JGKEZH-12548
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:964] - 创建子任务 5/11: CMS删除操作员逻辑调整 -> 主任务: JGKEZH-12542
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:964] - 创建子任务 5/11: CMS删除操作员逻辑调整 -> 主任务: JGKEZH-12542
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS删除操作员逻辑调整",
    "issuetype": {
      "id": "11015"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12542"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS删除操作员逻辑调整",
    "issuetype": {
      "id": "11015"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12542"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:36 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:37 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535707x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1FBDB1BA912771C34ED01C78A45031DF; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_8f5fcca58ba5ef25488810b4481f30c8bac3a12f_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'pq9x6l', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:37 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535707x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1FBDB1BA912771C34ED01C78A45031DF; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_8f5fcca58ba5ef25488810b4481f30c8bac3a12f_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'pq9x6l', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857180",
  "key": "JGKEZH-12549",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857180"
}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857180",
  "key": "JGKEZH-12549",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857180"
}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12549
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12549
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: CMS删除操作员逻辑调整 -> JGKEZH-12549
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: CMS删除操作员逻辑调整 -> JGKEZH-12549
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:964] - 创建子任务 6/11: 产品目录树列表和筛选 -> 主任务: JGKEZH-12543
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:964] - 创建子任务 6/11: 产品目录树列表和筛选 -> 主任务: JGKEZH-12543
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表和筛选",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12543"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表和筛选",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12543"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:38 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535708x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1CC55CE8AB03CCC2B1C3B7087B0BD982; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_6c4838e7ffc170cfe108c4318f1a39164b831804_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'ky1hho', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:38 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535708x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1CC55CE8AB03CCC2B1C3B7087B0BD982; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_6c4838e7ffc170cfe108c4318f1a39164b831804_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'ky1hho', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857181",
  "key": "JGKEZH-12550",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857181"
}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857181",
  "key": "JGKEZH-12550",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857181"
}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12550
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12550
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品目录树列表和筛选 -> JGKEZH-12550
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品目录树列表和筛选 -> JGKEZH-12550
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:964] - 创建子任务 7/11: 产品管理新增【关联产品目录树】选项 -> 主任务: JGKEZH-12543
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:964] - 创建子任务 7/11: 产品管理新增【关联产品目录树】选项 -> 主任务: JGKEZH-12543
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品管理新增【关联产品目录树】选项",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12543"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品管理新增【关联产品目录树】选项",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12543"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:37 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:38 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535709x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=22098910FF9A8D7855058A871188C458; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_179de8d9ae6afe5100038b80ab44db9e081a7643_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1rqinu4', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:38 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535709x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=22098910FF9A8D7855058A871188C458; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_179de8d9ae6afe5100038b80ab44db9e081a7643_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1rqinu4', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857182",
  "key": "JGKEZH-12551",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857182"
}
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857182",
  "key": "JGKEZH-12551",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857182"
}
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12551
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12551
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品管理新增【关联产品目录树】选项 -> JGKEZH-12551
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品管理新增【关联产品目录树】选项 -> JGKEZH-12551
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:964] - 创建子任务 8/11: 产品目录树列表 -> 主任务: JGKEZH-12543
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:964] - 创建子任务 8/11: 产品目录树列表 -> 主任务: JGKEZH-12543
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12543"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12543"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:38 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:39 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535710x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=37C2C8383970BFE5BD6486B9591391CB; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d592276771d39ca422f3ebf0e7fa621ff507dbc8_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1raxdo7', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:39 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535710x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=37C2C8383970BFE5BD6486B9591391CB; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d592276771d39ca422f3ebf0e7fa621ff507dbc8_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1raxdo7', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857183",
  "key": "JGKEZH-12552",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857183"
}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857183",
  "key": "JGKEZH-12552",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857183"
}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12552
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12552
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品目录树列表 -> JGKEZH-12552
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 产品目录树列表 -> JGKEZH-12552
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:964] - 创建子任务 9/11: 目录新增&编辑 -> 主任务: JGKEZH-12543
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:964] - 创建子任务 9/11: 目录新增&编辑 -> 主任务: JGKEZH-12543
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "目录新增&编辑",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12543"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "目录新增&编辑",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12543"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:40 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535711x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=95FB29E70DB56E33B770B3AE8E669F5E; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_46a9977c85ebb81725d540c8d1f963e26d63b761_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'ztocik', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:40 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535711x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=95FB29E70DB56E33B770B3AE8E669F5E; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_46a9977c85ebb81725d540c8d1f963e26d63b761_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'ztocik', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857184",
  "key": "JGKEZH-12553",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857184"
}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857184",
  "key": "JGKEZH-12553",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857184"
}
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12553
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12553
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 目录新增&编辑 -> JGKEZH-12553
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 目录新增&编辑 -> JGKEZH-12553
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:964] - 创建子任务 10/11: 筛选项新增 类型 -> 主任务: JGKEZH-12544
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:964] - 创建子任务 10/11: 筛选项新增 类型 -> 主任务: JGKEZH-12544
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:39 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "筛选项新增 类型",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12544"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.2d",
      "remainingEstimate": "0.2d"
    }
  }
}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "筛选项新增 类型",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12544"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.2d",
      "remainingEstimate": "0.2d"
    }
  }
}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:40 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535712x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=E8EDF50D39618C920C3DF10A3EEA6B98; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_ec4c30fdf2d4fe07b9ca90669dd4af140c7ff772_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'e2olzx', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:40 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535712x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=E8EDF50D39618C920C3DF10A3EEA6B98; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_ec4c30fdf2d4fe07b9ca90669dd4af140c7ff772_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'e2olzx', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857185",
  "key": "JGKEZH-12554",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857185"
}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857185",
  "key": "JGKEZH-12554",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857185"
}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12554
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12554
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 筛选项新增 类型 -> JGKEZH-12554
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 筛选项新增 类型 -> JGKEZH-12554
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:964] - 创建子任务 11/11: 开通列表支持类型筛选 -> 主任务: JGKEZH-12544
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:964] - 创建子任务 11/11: 开通列表支持类型筛选 -> 主任务: JGKEZH-12544
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:575] - ================================================================================
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:576] - JIRA API 创建sub_task请求详情:
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:577] - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:578] - 请求方法: POST
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:579] - 认证用户: lidezheng
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:584] - 请求头: {'Authorization': 'Bearer OTA3ODQ3Mz***'}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:585] - 请求体 (JSON):
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "开通列表支持类型筛选",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12544"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:586] - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "开通列表支持类型筛选",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12544"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:40 - jira_api - INFO - [jira_service.py:587] - ================================================================================
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:597] - JIRA API 创建sub_task响应详情:
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:598] - 响应状态码: 201
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:41 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535713x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=EBFC7C937DEFCFA541C3EEDC3B7DFB50; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_e53a3b960c271e1cb2c85cf708b102a68a5fbf28_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'elwhu6', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:599] - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Fri, 11 Jul 2025 05:47:41 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '829x535713x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=EBFC7C937DEFCFA541C3EEDC3B7DFB50; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_e53a3b960c271e1cb2c85cf708b102a68a5fbf28_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'elwhu6', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:600] - 响应体:
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857186",
  "key": "JGKEZH-12555",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857186"
}
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:603] - {
  "id": "857186",
  "key": "JGKEZH-12555",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857186"
}
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:606] - ================================================================================
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12555
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:611] - 成功创建sub_task: JGKEZH-12555
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 开通列表支持类型筛选 -> JGKEZH-12555
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:988] - 子任务创建成功: 开通列表支持类型筛选 -> JGKEZH-12555
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:1014] - 子任务创建完成: 成功 11 个，失败 0 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:1014] - 子任务创建完成: 成功 11 个，失败 0 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:372] - ================================================================================
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:373] - JIRA 批量任务创建完成:
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:373] - JIRA 批量任务创建完成:
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:374] - 总任务数: 11
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:374] - 总任务数: 11
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:375] - 主任务创建: 5 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:375] - 主任务创建: 5 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:376] - 子任务创建: 11 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:376] - 子任务创建: 11 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:377] - Sprint关联: 5 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:377] - Sprint关联: 5 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:378] - 需求关联: 5 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:378] - 需求关联: 5 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:379] - 成功: 16 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:379] - 成功: 16 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:380] - 失败: 0 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:380] - 失败: 0 个
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:381] - ================================================================================
2025-07-11 13:47:41 - jira_api - INFO - [jira_service.py:381] - ================================================================================
