2025-07-22 16:42:59 - root - INFO - 日志系统初始化完成
2025-07-22 16:42:59 - root - INFO - 日志级别: INFO
2025-07-22 16:42:59 - root - INFO - 应用日志文件: logs/app_20250722.log
2025-07-22 16:42:59 - root - INFO - JIRA API日志文件: logs/jira_api_20250722.log
2025-07-22 16:43:21 - root - INFO - Excel文件实际列名: ['橙卡', '模块', '主任务', '子任务', '任务类型', '负责人', '工作量']
2025-07-22 16:43:21 - root - INFO - 列名映射: {'橙卡': '橙卡', '模块': '模块', '主任务': '主任务', '子任务': '子任务', '任务类型': '任务类型', '负责人': '负责人', '工作量': '工作量'}
2025-07-22 16:43:21 - root - INFO - 处理合并单元格后的数据预览:
             橙卡     模块              主任务           子任务 任务类型  负责人  工作量
0  JGKEZH-11433  三季度优化      CMS图片裁剪功能优化  多个场景设定图片上传比例   UI   关远  0.3
1  JGKEZH-11433  三季度优化        CMS代办通知邮件    发送邮件模板通知调整   后端  林文杰  0.3
2  JGKEZH-11433  三季度优化  PB系统支持退回删除操作员申请     cms审核支持退回   UI   关远  0.1
3  JGKEZH-11433  三季度优化  PB系统支持退回删除操作员申请  web删除操作员逻辑调整  API  吴泳琳  1.0
4  JGKEZH-11433  三季度优化  PB系统支持退回删除操作员申请  CMS删除操作员逻辑调整   测试  林文杰  1.0
2025-07-22 16:43:27 - root - INFO - ================================================================================
2025-07-22 16:43:27 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-22 16:43:27 - root - INFO - 任务数量: 11
2025-07-22 16:43:27 - root - INFO - 环境: test
2025-07-22 16:43:27 - root - INFO - 用户: lidezheng
2025-07-22 16:43:27 - root - INFO - 项目: JGKEZH
2025-07-22 16:43:27 - root - INFO - Sprint: INST2025-Sprint10
2025-07-22 16:43:27 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-22 16:43:27 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10",
  "test_assignee": "zhouqishu"
}
2025-07-22 16:43:27 - root - INFO - 任务详情:
2025-07-22 16:43:27 - root - INFO -   任务 1: 多个场景设定图片上传比例 - 负责人: 关远
2025-07-22 16:43:27 - root - INFO -   任务 2: 发送邮件模板通知调整 - 负责人: 林文杰
2025-07-22 16:43:27 - root - INFO -   任务 3: cms审核支持退回 - 负责人: 关远
2025-07-22 16:43:27 - root - INFO -   任务 4: web删除操作员逻辑调整 - 负责人: 吴泳琳
2025-07-22 16:43:27 - root - INFO -   任务 5: CMS删除操作员逻辑调整 - 负责人: 林文杰
2025-07-22 16:43:27 - root - INFO -   任务 6: 产品目录树列表和筛选 - 负责人: 关远
2025-07-22 16:43:27 - root - INFO -   任务 7: 产品管理新增【关联产品目录树】选项 - 负责人: 关远
2025-07-22 16:43:27 - root - INFO -   任务 8: 产品目录树列表 - 负责人: 林文杰
2025-07-22 16:43:27 - root - INFO -   任务 9: 目录新增&编辑 - 负责人: 林文杰
2025-07-22 16:43:27 - root - INFO -   任务 10: 筛选项新增 类型 - 负责人: 关远
2025-07-22 16:43:27 - root - INFO -   任务 11: 开通列表支持类型筛选 - 负责人: 林文杰
2025-07-22 16:43:27 - root - INFO - ================================================================================
2025-07-22 16:43:27 - jira_api - INFO - ================================================================================
2025-07-22 16:43:27 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-22 16:43:27 - jira_api - INFO - 任务数量: 11
2025-07-22 16:43:27 - jira_api - INFO - 环境: test
2025-07-22 16:43:27 - jira_api - INFO - 项目: JGKEZH
2025-07-22 16:43:27 - jira_api - INFO - 用户: lidezheng
2025-07-22 16:43:27 - jira_api - INFO - Sprint: INST2025-Sprint10
2025-07-22 16:43:27 - jira_api - INFO - ================================================================================
2025-07-22 16:43:27 - jira_api - INFO - 开始分析任务层次结构...
2025-07-22 16:43:27 - jira_api - INFO - 发现主任务: CMS图片裁剪功能优化
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-22 16:43:27 - jira_api - INFO - 发现主任务: CMS代办通知邮件
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-22 16:43:27 - jira_api - INFO - 发现主任务: PB系统支持退回删除操作员申请
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-22 16:43:27 - jira_api - INFO - 发现主任务: CMS端「产品目录树」
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-22 16:43:27 - jira_api - INFO - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-22 16:43:27 - jira_api - INFO - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-22 16:43:27 - jira_api - INFO - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-22 16:43:27 - jira_api - INFO - 分析结果: 5 个主任务, 11 个子任务
2025-07-22 16:43:27 - jira_api - INFO - ============================================================
2025-07-22 16:43:27 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-22 16:43:27 - jira_api - INFO - 需要创建 5 个主任务
2025-07-22 16:43:27 - jira_api - INFO - ============================================================
2025-07-22 16:43:27 - jira_api - INFO - 创建主任务: CMS图片裁剪功能优化
2025-07-22 16:43:28 - jira_api - INFO - ================================================================================
2025-07-22 16:43:28 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:43:28 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:28 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:28 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:28 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:28 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:28 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:43:28 - jira_api - INFO - ================================================================================
2025-07-22 16:43:29 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:43:29 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:29 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:29 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592249x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=E69EB2F67C5D76D1F06E6AF41A01F514; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_36c3ce78c04cea0e4d514a4257c6b01edd869070_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1tdt5ly', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:29 - jira_api - INFO - 响应体:
2025-07-22 16:43:29 - jira_api - INFO - {
  "id": "857318",
  "key": "JGKEZH-12679",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857318"
}
2025-07-22 16:43:29 - jira_api - INFO - ================================================================================
2025-07-22 16:43:29 - jira_api - INFO - 成功创建main_task: JGKEZH-12679
2025-07-22 16:43:29 - jira_api - INFO - 主任务创建成功: CMS图片裁剪功能优化 -> JGKEZH-12679
2025-07-22 16:43:29 - jira_api - INFO - 创建主任务: CMS代办通知邮件
2025-07-22 16:43:29 - jira_api - INFO - ================================================================================
2025-07-22 16:43:29 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:43:29 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:29 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:29 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:29 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:29 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:29 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:43:29 - jira_api - INFO - ================================================================================
2025-07-22 16:43:30 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:43:30 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:30 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:29 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592250x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=D110BD61321C6CD38B43686D4ACCA7AD; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_794539f71134d4c235244b7d193264396b23db96_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1qzd9ez', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:30 - jira_api - INFO - 响应体:
2025-07-22 16:43:30 - jira_api - INFO - {
  "id": "857319",
  "key": "JGKEZH-12680",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857319"
}
2025-07-22 16:43:30 - jira_api - INFO - ================================================================================
2025-07-22 16:43:30 - jira_api - INFO - 成功创建main_task: JGKEZH-12680
2025-07-22 16:43:30 - jira_api - INFO - 主任务创建成功: CMS代办通知邮件 -> JGKEZH-12680
2025-07-22 16:43:30 - jira_api - INFO - 创建主任务: PB系统支持退回删除操作员申请
2025-07-22 16:43:30 - jira_api - INFO - ================================================================================
2025-07-22 16:43:30 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:43:30 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:30 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:30 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:30 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:30 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:30 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:43:30 - jira_api - INFO - ================================================================================
2025-07-22 16:43:30 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:43:30 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:30 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:30 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592251x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=2457BE80F5EB383C345FC90791D8211B; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_db523d68db93b93e4d9ab6a4ea8d4871e358bf0a_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '67u9co', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:30 - jira_api - INFO - 响应体:
2025-07-22 16:43:30 - jira_api - INFO - {
  "id": "857320",
  "key": "JGKEZH-12681",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857320"
}
2025-07-22 16:43:30 - jira_api - INFO - ================================================================================
2025-07-22 16:43:30 - jira_api - INFO - 成功创建main_task: JGKEZH-12681
2025-07-22 16:43:30 - jira_api - INFO - 主任务创建成功: PB系统支持退回删除操作员申请 -> JGKEZH-12681
2025-07-22 16:43:30 - jira_api - INFO - 创建主任务: CMS端「产品目录树」
2025-07-22 16:43:30 - jira_api - INFO - ================================================================================
2025-07-22 16:43:30 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:43:30 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:30 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:30 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:30 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:30 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:30 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:43:30 - jira_api - INFO - ================================================================================
2025-07-22 16:43:31 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:43:31 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:31 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:31 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592252x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=08B60F80F7670A84B53E518E028710F9; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d8d9878308b8de9ccb292d2b51b539678cfbec30_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'lz14td', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:31 - jira_api - INFO - 响应体:
2025-07-22 16:43:31 - jira_api - INFO - {
  "id": "857321",
  "key": "JGKEZH-12682",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857321"
}
2025-07-22 16:43:31 - jira_api - INFO - ================================================================================
2025-07-22 16:43:31 - jira_api - INFO - 成功创建main_task: JGKEZH-12682
2025-07-22 16:43:31 - jira_api - INFO - 主任务创建成功: CMS端「产品目录树」 -> JGKEZH-12682
2025-07-22 16:43:31 - jira_api - INFO - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-22 16:43:31 - jira_api - INFO - ================================================================================
2025-07-22 16:43:31 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:43:31 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:31 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:31 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:31 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:31 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:31 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:43:31 - jira_api - INFO - ================================================================================
2025-07-22 16:43:31 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:43:31 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:31 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:31 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592253x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=97FFAD299A6F46152F2F98F304D34696; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_db496ca40cef7a95030ee3da8db58a2b620bd207_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1raujfw', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:31 - jira_api - INFO - 响应体:
2025-07-22 16:43:31 - jira_api - INFO - {
  "id": "857322",
  "key": "JGKEZH-12683",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857322"
}
2025-07-22 16:43:31 - jira_api - INFO - ================================================================================
2025-07-22 16:43:31 - jira_api - INFO - 成功创建main_task: JGKEZH-12683
2025-07-22 16:43:31 - jira_api - INFO - 主任务创建成功: CMS「产品管理-开通列表」优化 -> JGKEZH-12683
2025-07-22 16:43:31 - jira_api - INFO - 主任务创建完成: 成功 5 个，失败 0 个
2025-07-22 16:43:31 - jira_api - INFO - ============================================================
2025-07-22 16:43:31 - jira_api - INFO - 步骤2: 开始Sprint关联
2025-07-22 16:43:31 - jira_api - INFO - Sprint名称: INST2025-Sprint10
2025-07-22 16:43:31 - jira_api - INFO - 需要关联 5 个主任务
2025-07-22 16:43:31 - jira_api - INFO - ============================================================
2025-07-22 16:43:31 - jira_api - INFO - 查询Sprint ID: http://jirauat.gf.com.cn/rest/greenhopper/1.0/sprint/picker?query=INST2025-Sprint10
2025-07-22 16:43:31 - jira_api - INFO - Sprint查询请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Accept': 'application/json'}
2025-07-22 16:43:32 - jira_api - INFO - Sprint查询响应状态码: 200
2025-07-22 16:43:32 - jira_api - INFO - Sprint查询响应: {"suggestions":[],"allMatches":[]}
2025-07-22 16:43:32 - jira_api - ERROR - 未找到Sprint: INST2025-Sprint10
2025-07-22 16:43:32 - jira_api - ERROR - 未找到Sprint: INST2025-Sprint10
2025-07-22 16:43:32 - jira_api - INFO - Sprint关联完成: 成功 0 个
2025-07-22 16:43:32 - jira_api - INFO - ============================================================
2025-07-22 16:43:32 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-22 16:43:32 - jira_api - INFO - ============================================================
2025-07-22 16:43:32 - jira_api - INFO - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12679
2025-07-22 16:43:32 - jira_api - INFO - 关联需求 JGKEZH-11433 到任务 JGKEZH-12679
2025-07-22 16:43:32 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12679
2025-07-22 16:43:32 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:43:32 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-22 16:43:32 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:43:32 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:43:32 - jira_api - INFO - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12679
2025-07-22 16:43:32 - jira_api - INFO - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12680
2025-07-22 16:43:32 - jira_api - INFO - 关联需求 JGKEZH-11433 到任务 JGKEZH-12680
2025-07-22 16:43:32 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12680
2025-07-22 16:43:32 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:43:32 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-22 16:43:32 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:43:32 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:43:32 - jira_api - INFO - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12680
2025-07-22 16:43:32 - jira_api - INFO - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12681
2025-07-22 16:43:32 - jira_api - INFO - 关联需求 JGKEZH-11433 到任务 JGKEZH-12681
2025-07-22 16:43:32 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12681
2025-07-22 16:43:32 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:43:32 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-22 16:43:32 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:43:32 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:43:32 - jira_api - INFO - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12681
2025-07-22 16:43:32 - jira_api - INFO - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12682
2025-07-22 16:43:32 - jira_api - INFO - 关联需求 JGKEZH-11521 到任务 JGKEZH-12682
2025-07-22 16:43:32 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12682
2025-07-22 16:43:32 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:43:32 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-22 16:43:33 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:43:33 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:43:33 - jira_api - INFO - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12682
2025-07-22 16:43:33 - jira_api - INFO - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12683
2025-07-22 16:43:33 - jira_api - INFO - 关联需求 JGKEZH-11521 到任务 JGKEZH-12683
2025-07-22 16:43:33 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12683
2025-07-22 16:43:33 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:43:33 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-22 16:43:33 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:43:33 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:43:33 - jira_api - INFO - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12683
2025-07-22 16:43:33 - jira_api - INFO - 需求关联完成: 成功 5 个
2025-07-22 16:43:33 - jira_api - INFO - ============================================================
2025-07-22 16:43:33 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-22 16:43:33 - jira_api - INFO - 需要创建 11 个子任务
2025-07-22 16:43:33 - jira_api - INFO - 成功创建的主任务数量: 5
2025-07-22 16:43:33 - jira_api - INFO - ============================================================
2025-07-22 16:43:33 - jira_api - INFO - 有效子任务: 11 个
2025-07-22 16:43:33 - jira_api - INFO - 因主任务失败而跳过的子任务: 0 个
2025-07-22 16:43:33 - jira_api - INFO - 创建子任务 1/11: 多个场景设定图片上传比例 -> 主任务: JGKEZH-12679
2025-07-22 16:43:33 - jira_api - INFO - ================================================================================
2025-07-22 16:43:33 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:33 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:33 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:33 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:33 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:33 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:33 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12679"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-22 16:43:33 - jira_api - INFO - ================================================================================
2025-07-22 16:43:34 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:34 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:34 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:33 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592260x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=C8E82ED026E4E771B2F88FF87EA33999; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d348efff1bb28835acd50aeda770f8eb06154de4_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '5lygcj', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:34 - jira_api - INFO - 响应体:
2025-07-22 16:43:34 - jira_api - INFO - {
  "id": "857323",
  "key": "JGKEZH-12684",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857323"
}
2025-07-22 16:43:34 - jira_api - INFO - ================================================================================
2025-07-22 16:43:34 - jira_api - INFO - 成功创建sub_task: JGKEZH-12684
2025-07-22 16:43:34 - jira_api - INFO - 子任务创建成功: 多个场景设定图片上传比例 -> JGKEZH-12684
2025-07-22 16:43:34 - jira_api - INFO - 创建子任务 2/11: 发送邮件模板通知调整 -> 主任务: JGKEZH-12680
2025-07-22 16:43:34 - jira_api - INFO - ================================================================================
2025-07-22 16:43:34 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:34 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:34 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:34 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:34 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:34 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:34 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "发送邮件模板通知调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12680"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-22 16:43:34 - jira_api - INFO - ================================================================================
2025-07-22 16:43:34 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:34 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:34 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:34 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592261x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=0FA7828593369CF964EDDE713C625850; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_10a1122c850c7a94f7c16f0b16a4167f149150e8_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1wiy03k', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:34 - jira_api - INFO - 响应体:
2025-07-22 16:43:34 - jira_api - INFO - {
  "id": "857324",
  "key": "JGKEZH-12685",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857324"
}
2025-07-22 16:43:34 - jira_api - INFO - ================================================================================
2025-07-22 16:43:34 - jira_api - INFO - 成功创建sub_task: JGKEZH-12685
2025-07-22 16:43:34 - jira_api - INFO - 子任务创建成功: 发送邮件模板通知调整 -> JGKEZH-12685
2025-07-22 16:43:34 - jira_api - INFO - 创建子任务 3/11: cms审核支持退回 -> 主任务: JGKEZH-12681
2025-07-22 16:43:34 - jira_api - INFO - ================================================================================
2025-07-22 16:43:34 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:34 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:34 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:34 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:34 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:34 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:34 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "cms审核支持退回",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12681"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.1d",
      "remainingEstimate": "0.1d"
    }
  }
}
2025-07-22 16:43:34 - jira_api - INFO - ================================================================================
2025-07-22 16:43:35 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:35 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:35 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:35 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592262x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=F88C74269D108087719AA7B18952DBA1; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_800b68b0a77a0eff5a5bc7f4f51f205465b6feb2_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '18nysxn', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:35 - jira_api - INFO - 响应体:
2025-07-22 16:43:35 - jira_api - INFO - {
  "id": "857325",
  "key": "JGKEZH-12686",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857325"
}
2025-07-22 16:43:35 - jira_api - INFO - ================================================================================
2025-07-22 16:43:35 - jira_api - INFO - 成功创建sub_task: JGKEZH-12686
2025-07-22 16:43:35 - jira_api - INFO - 子任务创建成功: cms审核支持退回 -> JGKEZH-12686
2025-07-22 16:43:35 - jira_api - INFO - 创建子任务 4/11: web删除操作员逻辑调整 -> 主任务: JGKEZH-12681
2025-07-22 16:43:35 - jira_api - INFO - ================================================================================
2025-07-22 16:43:35 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:35 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:35 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:35 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:35 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:35 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:35 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "web删除操作员逻辑调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12681"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "wuyonglin"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-22 16:43:35 - jira_api - INFO - ================================================================================
2025-07-22 16:43:35 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:35 - jira_api - INFO - 响应状态码: 400
2025-07-22 16:43:35 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:35 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592263x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=0D6F89ECA0D76135C5A68DD3E17A5BB6; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_810f74fcdd7fab998f78666ce861a04a458739ac_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1mvpotr', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:35 - jira_api - INFO - 响应体:
2025-07-22 16:43:35 - jira_api - INFO - {
  "errorMessages": [],
  "errors": {
    "assignee": "用户 'wuyonglin' 不存在。"
  }
}
2025-07-22 16:43:35 - jira_api - INFO - ================================================================================
2025-07-22 16:43:35 - jira_api - ERROR - 创建sub_task失败: JIRA API错误: 400 - 
2025-07-22 16:43:35 - jira_api - ERROR - 创建sub_task异常: JIRA API错误: 400 - 
2025-07-22 16:43:35 - jira_api - ERROR - 子任务 'web删除操作员逻辑调整' 创建失败: 创建子任务失败: JIRA API错误: 400 - 
2025-07-22 16:43:35 - jira_api - INFO - 创建子任务 5/11: CMS删除操作员逻辑调整 -> 主任务: JGKEZH-12681
2025-07-22 16:43:35 - jira_api - INFO - ================================================================================
2025-07-22 16:43:35 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:35 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:35 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:35 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:35 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:35 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:35 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS删除操作员逻辑调整",
    "issuetype": {
      "id": "11015"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12681"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-22 16:43:35 - jira_api - INFO - ================================================================================
2025-07-22 16:43:36 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:36 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:36 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:36 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592264x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=0FB31E65B9256243CA808D9B5C689AF7; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_a6299a9108bf71db36b97441724b45db730d985e_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '16pqfen', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:36 - jira_api - INFO - 响应体:
2025-07-22 16:43:36 - jira_api - INFO - {
  "id": "857326",
  "key": "JGKEZH-12687",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857326"
}
2025-07-22 16:43:36 - jira_api - INFO - ================================================================================
2025-07-22 16:43:36 - jira_api - INFO - 成功创建sub_task: JGKEZH-12687
2025-07-22 16:43:36 - jira_api - INFO - 子任务创建成功: CMS删除操作员逻辑调整 -> JGKEZH-12687
2025-07-22 16:43:36 - jira_api - INFO - 创建子任务 6/11: 产品目录树列表和筛选 -> 主任务: JGKEZH-12682
2025-07-22 16:43:36 - jira_api - INFO - ================================================================================
2025-07-22 16:43:36 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:36 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:36 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:36 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:36 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:36 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:36 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表和筛选",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12682"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-22 16:43:36 - jira_api - INFO - ================================================================================
2025-07-22 16:43:37 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:37 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:37 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:36 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592265x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=DACFF741DFA3DA0E322459DE1A9DEE23; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_542e2c78e29f7efa2ad4eeb5f97e734549331f3b_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1gq3nlc', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:37 - jira_api - INFO - 响应体:
2025-07-22 16:43:37 - jira_api - INFO - {
  "id": "857327",
  "key": "JGKEZH-12688",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857327"
}
2025-07-22 16:43:37 - jira_api - INFO - ================================================================================
2025-07-22 16:43:37 - jira_api - INFO - 成功创建sub_task: JGKEZH-12688
2025-07-22 16:43:37 - jira_api - INFO - 子任务创建成功: 产品目录树列表和筛选 -> JGKEZH-12688
2025-07-22 16:43:37 - jira_api - INFO - 创建子任务 7/11: 产品管理新增【关联产品目录树】选项 -> 主任务: JGKEZH-12682
2025-07-22 16:43:37 - jira_api - INFO - ================================================================================
2025-07-22 16:43:37 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:37 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:37 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:37 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:37 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:37 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:37 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品管理新增【关联产品目录树】选项",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12682"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-22 16:43:37 - jira_api - INFO - ================================================================================
2025-07-22 16:43:37 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:37 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:37 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:37 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592266x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1CBD1181338B5587C8695FD64020D657; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_61b30bbab27dba9e75010fc165514a2d425a57be_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1knu4sz', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:37 - jira_api - INFO - 响应体:
2025-07-22 16:43:37 - jira_api - INFO - {
  "id": "857328",
  "key": "JGKEZH-12689",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857328"
}
2025-07-22 16:43:37 - jira_api - INFO - ================================================================================
2025-07-22 16:43:37 - jira_api - INFO - 成功创建sub_task: JGKEZH-12689
2025-07-22 16:43:37 - jira_api - INFO - 子任务创建成功: 产品管理新增【关联产品目录树】选项 -> JGKEZH-12689
2025-07-22 16:43:37 - jira_api - INFO - 创建子任务 8/11: 产品目录树列表 -> 主任务: JGKEZH-12682
2025-07-22 16:43:37 - jira_api - INFO - ================================================================================
2025-07-22 16:43:37 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:37 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:37 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:37 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:37 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:37 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:37 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12682"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-22 16:43:37 - jira_api - INFO - ================================================================================
2025-07-22 16:43:38 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:38 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:38 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:38 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592267x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=22CE90B11ED2608A42B300970980DC42; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d6d3a722e1ba87592a2939bd2ea558ec62e91fec_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1tq32lm', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:38 - jira_api - INFO - 响应体:
2025-07-22 16:43:38 - jira_api - INFO - {
  "id": "857329",
  "key": "JGKEZH-12690",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857329"
}
2025-07-22 16:43:38 - jira_api - INFO - ================================================================================
2025-07-22 16:43:38 - jira_api - INFO - 成功创建sub_task: JGKEZH-12690
2025-07-22 16:43:38 - jira_api - INFO - 子任务创建成功: 产品目录树列表 -> JGKEZH-12690
2025-07-22 16:43:38 - jira_api - INFO - 创建子任务 9/11: 目录新增&编辑 -> 主任务: JGKEZH-12682
2025-07-22 16:43:38 - jira_api - INFO - ================================================================================
2025-07-22 16:43:38 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:38 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:38 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:38 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:38 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:38 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:38 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "目录新增&编辑",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12682"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-22 16:43:38 - jira_api - INFO - ================================================================================
2025-07-22 16:43:39 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:39 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:39 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:38 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592268x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=5531B6F903EA136F50ACCCCB9814285D; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_2f58d7cfa11292c9f1d4bc644693db756f5df88b_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '14v2yhj', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:39 - jira_api - INFO - 响应体:
2025-07-22 16:43:39 - jira_api - INFO - {
  "id": "857330",
  "key": "JGKEZH-12691",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857330"
}
2025-07-22 16:43:39 - jira_api - INFO - ================================================================================
2025-07-22 16:43:39 - jira_api - INFO - 成功创建sub_task: JGKEZH-12691
2025-07-22 16:43:39 - jira_api - INFO - 子任务创建成功: 目录新增&编辑 -> JGKEZH-12691
2025-07-22 16:43:39 - jira_api - INFO - 创建子任务 10/11: 筛选项新增 类型 -> 主任务: JGKEZH-12683
2025-07-22 16:43:39 - jira_api - INFO - ================================================================================
2025-07-22 16:43:39 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:39 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:39 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:39 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:39 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:39 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:39 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "筛选项新增 类型",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12683"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.2d",
      "remainingEstimate": "0.2d"
    }
  }
}
2025-07-22 16:43:39 - jira_api - INFO - ================================================================================
2025-07-22 16:43:40 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:40 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:40 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:39 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592269x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=E55B12FF81AC5BCED57B6A5D1A3AC19B; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_c83ed42226ec35aa7efab915ff219310115b851c_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'xayap0', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:40 - jira_api - INFO - 响应体:
2025-07-22 16:43:40 - jira_api - INFO - {
  "id": "857331",
  "key": "JGKEZH-12692",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857331"
}
2025-07-22 16:43:40 - jira_api - INFO - ================================================================================
2025-07-22 16:43:40 - jira_api - INFO - 成功创建sub_task: JGKEZH-12692
2025-07-22 16:43:40 - jira_api - INFO - 子任务创建成功: 筛选项新增 类型 -> JGKEZH-12692
2025-07-22 16:43:40 - jira_api - INFO - 创建子任务 11/11: 开通列表支持类型筛选 -> 主任务: JGKEZH-12683
2025-07-22 16:43:40 - jira_api - INFO - ================================================================================
2025-07-22 16:43:40 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:43:40 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:43:40 - jira_api - INFO - 请求方法: POST
2025-07-22 16:43:40 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:43:40 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:43:40 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:43:40 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "开通列表支持类型筛选",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12683"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-22 16:43:40 - jira_api - INFO - ================================================================================
2025-07-22 16:43:40 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:43:40 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:43:40 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:43:40 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1005x592270x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=FFA346AFA891B980285B2309B7E694C9; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_23c38d6cc11caf7dba080dd8dbcd54603b8547b6_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'bxutom', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:43:40 - jira_api - INFO - 响应体:
2025-07-22 16:43:40 - jira_api - INFO - {
  "id": "857332",
  "key": "JGKEZH-12693",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857332"
}
2025-07-22 16:43:40 - jira_api - INFO - ================================================================================
2025-07-22 16:43:40 - jira_api - INFO - 成功创建sub_task: JGKEZH-12693
2025-07-22 16:43:40 - jira_api - INFO - 子任务创建成功: 开通列表支持类型筛选 -> JGKEZH-12693
2025-07-22 16:43:40 - jira_api - INFO - 子任务创建完成: 成功 10 个，失败 1 个
2025-07-22 16:43:40 - jira_api - INFO - ================================================================================
2025-07-22 16:43:40 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-22 16:43:40 - jira_api - INFO - 总任务数: 11
2025-07-22 16:43:40 - jira_api - INFO - 主任务创建: 5 个
2025-07-22 16:43:40 - jira_api - INFO - 子任务创建: 10 个
2025-07-22 16:43:40 - jira_api - INFO - Sprint关联: 0 个
2025-07-22 16:43:40 - jira_api - INFO - 需求关联: 5 个
2025-07-22 16:43:40 - jira_api - INFO - 成功: 15 个
2025-07-22 16:43:40 - jira_api - INFO - 失败: 2 个
2025-07-22 16:43:40 - jira_api - INFO - ================================================================================
2025-07-22 16:43:40 - root - INFO - ================================================================================
2025-07-22 16:43:40 - root - INFO - JIRA批量任务创建结果:
2025-07-22 16:43:40 - root - INFO - 总任务数: 11
2025-07-22 16:43:40 - root - INFO - 主任务创建: 5 个
2025-07-22 16:43:40 - root - INFO - 子任务创建: 10 个
2025-07-22 16:43:40 - root - INFO - Sprint关联: 0 个
2025-07-22 16:43:40 - root - INFO - 需求关联: 5 个
2025-07-22 16:43:40 - root - INFO - 成功: 15 个
2025-07-22 16:43:40 - root - INFO - 失败: 2 个
2025-07-22 16:43:40 - root - INFO - 返回结果:
2025-07-22 16:43:40 - root - INFO - {
  "success": true,
  "data": {
    "success": [
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12679",
        "title": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12679"
      },
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12680",
        "title": "CMS代办通知邮件",
        "assignee": "林文杰",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12680"
      },
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12681",
        "title": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12681"
      },
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12682",
        "title": "CMS端「产品目录树」",
        "assignee": "关远",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12682"
      },
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12683",
        "title": "CMS「产品管理-开通列表」优化",
        "assignee": "关远",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12683"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12684",
        "title": "多个场景设定图片上传比例",
        "parent_key": "JGKEZH-12679",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12684"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12685",
        "title": "发送邮件模板通知调整",
        "parent_key": "JGKEZH-12680",
        "assignee": "林文杰",
        "task_type": "API",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12685"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12686",
        "title": "cms审核支持退回",
        "parent_key": "JGKEZH-12681",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12686"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12687",
        "title": "CMS删除操作员逻辑调整",
        "parent_key": "JGKEZH-12681",
        "assignee": "林文杰",
        "task_type": "测试",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12687"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12688",
        "title": "产品目录树列表和筛选",
        "parent_key": "JGKEZH-12682",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12688"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12689",
        "title": "产品管理新增【关联产品目录树】选项",
        "parent_key": "JGKEZH-12682",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12689"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12690",
        "title": "产品目录树列表",
        "parent_key": "JGKEZH-12682",
        "assignee": "林文杰",
        "task_type": "API",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12690"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12691",
        "title": "目录新增&编辑",
        "parent_key": "JGKEZH-12682",
        "assignee": "林文杰",
        "task_type": "API",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12691"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12692",
        "title": "筛选项新增 类型",
        "parent_key": "JGKEZH-12683",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12692"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12693",
        "title": "开通列表支持类型筛选",
        "parent_key": "JGKEZH-12683",
        "assignee": "林文杰",
        "task_type": "API",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12693"
      }
    ],
    "failed": [
      {
        "success": false,
        "type": "sprint_link",
        "title": "Sprint关联",
        "error": "未找到Sprint: INST2025-Sprint10"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "web删除操作员逻辑调整",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "吴泳琳",
        "task_type": "API",
        "error": "创建子任务失败: JIRA API错误: 400 - ",
        "demand": "JGKEZH-11433",
        "module": "三季度优化",
        "sub_task": "web删除操作员逻辑调整",
        "workload": "1.0"
      }
    ],
    "summary": {
      "total": 11,
      "success_count": 15,
      "failed_count": 2,
      "main_tasks_created": 5,
      "sub_tasks_created": 10,
      "sprint_linked": 0,
      "demands_linked": 5
    }
  }
}
2025-07-22 16:43:40 - root - INFO - ================================================================================
2025-07-22 16:44:03 - root - INFO - ================================================================================
2025-07-22 16:44:03 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-22 16:44:03 - root - INFO - 任务数量: 1
2025-07-22 16:44:03 - root - INFO - 环境: test
2025-07-22 16:44:03 - root - INFO - 用户: lidezheng
2025-07-22 16:44:03 - root - INFO - 项目: JGKEZH
2025-07-22 16:44:03 - root - INFO - Sprint: INST2025-Sprint10
2025-07-22 16:44:03 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-22 16:44:03 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10",
  "test_assignee": "zhouqishu"
}
2025-07-22 16:44:03 - root - INFO - 任务详情:
2025-07-22 16:44:03 - root - INFO -   任务 1: web删除操作员逻辑调整 - 负责人: 吴泳琳
2025-07-22 16:44:03 - root - INFO - ================================================================================
2025-07-22 16:44:03 - jira_api - INFO - ================================================================================
2025-07-22 16:44:03 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-22 16:44:03 - jira_api - INFO - 任务数量: 1
2025-07-22 16:44:03 - jira_api - INFO - 环境: test
2025-07-22 16:44:03 - jira_api - INFO - 项目: JGKEZH
2025-07-22 16:44:03 - jira_api - INFO - 用户: lidezheng
2025-07-22 16:44:03 - jira_api - INFO - Sprint: INST2025-Sprint10
2025-07-22 16:44:03 - jira_api - INFO - ================================================================================
2025-07-22 16:44:03 - jira_api - INFO - 开始分析任务层次结构...
2025-07-22 16:44:03 - jira_api - INFO - 发现主任务: PB系统支持退回删除操作员申请
2025-07-22 16:44:03 - jira_api - INFO - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-22 16:44:03 - jira_api - INFO - 任务结构分析完成: 1 个主任务, 1 个子任务
2025-07-22 16:44:03 - jira_api - INFO - 分析结果: 1 个主任务, 1 个子任务
2025-07-22 16:44:03 - jira_api - INFO - ============================================================
2025-07-22 16:44:03 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-22 16:44:03 - jira_api - INFO - 需要创建 1 个主任务
2025-07-22 16:44:03 - jira_api - INFO - ============================================================
2025-07-22 16:44:03 - jira_api - INFO - 创建主任务: PB系统支持退回删除操作员申请
2025-07-22 16:44:03 - jira_api - INFO - ================================================================================
2025-07-22 16:44:03 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:44:03 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:44:03 - jira_api - INFO - 请求方法: POST
2025-07-22 16:44:03 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:44:03 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:44:03 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:44:03 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "wuyonglin"
    },
    "customfield_13103": {
      "name": "wuyonglin"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:44:03 - jira_api - INFO - ================================================================================
2025-07-22 16:44:03 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:44:03 - jira_api - INFO - 响应状态码: 400
2025-07-22 16:44:03 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:44:03 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1006x592273x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=21ADEDA2F54C1FC910062DA746920C65; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_ae599e4fed65642923279d1f911b2f1797c08b89_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1nrhuf9', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:44:03 - jira_api - INFO - 响应体:
2025-07-22 16:44:03 - jira_api - INFO - {
  "errorMessages": [],
  "errors": {
    "customfield_13103": "未在系统中找到用户 “wuyonglin”。",
    "assignee": "用户 'wuyonglin' 不存在。"
  }
}
2025-07-22 16:44:03 - jira_api - INFO - ================================================================================
2025-07-22 16:44:03 - jira_api - ERROR - 创建main_task失败: JIRA API错误: 400 - 
2025-07-22 16:44:03 - jira_api - ERROR - 创建main_task异常: JIRA API错误: 400 - 
2025-07-22 16:44:03 - jira_api - ERROR - 主任务 'PB系统支持退回删除操作员申请' 创建失败: 创建主任务失败: JIRA API错误: 400 - 
2025-07-22 16:44:03 - jira_api - INFO - 主任务创建完成: 成功 0 个，失败 1 个
2025-07-22 16:44:03 - jira_api - INFO - ============================================================
2025-07-22 16:44:03 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-22 16:44:03 - jira_api - INFO - ============================================================
2025-07-22 16:44:03 - jira_api - INFO - 需求关联完成: 成功 0 个
2025-07-22 16:44:03 - jira_api - INFO - ============================================================
2025-07-22 16:44:03 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-22 16:44:03 - jira_api - INFO - 需要创建 1 个子任务
2025-07-22 16:44:03 - jira_api - INFO - 成功创建的主任务数量: 0
2025-07-22 16:44:03 - jira_api - INFO - ============================================================
2025-07-22 16:44:03 - jira_api - ERROR - 主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'web删除操作员逻辑调整' 无法创建
2025-07-22 16:44:03 - jira_api - INFO - 有效子任务: 0 个
2025-07-22 16:44:03 - jira_api - INFO - 因主任务失败而跳过的子任务: 1 个
2025-07-22 16:44:03 - jira_api - INFO - 子任务创建完成: 成功 0 个，失败 1 个
2025-07-22 16:44:03 - jira_api - INFO - ================================================================================
2025-07-22 16:44:03 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-22 16:44:03 - jira_api - INFO - 总任务数: 1
2025-07-22 16:44:03 - jira_api - INFO - 主任务创建: 0 个
2025-07-22 16:44:03 - jira_api - INFO - 子任务创建: 0 个
2025-07-22 16:44:03 - jira_api - INFO - Sprint关联: 0 个
2025-07-22 16:44:03 - jira_api - INFO - 需求关联: 0 个
2025-07-22 16:44:03 - jira_api - INFO - 成功: 0 个
2025-07-22 16:44:03 - jira_api - INFO - 失败: 2 个
2025-07-22 16:44:03 - jira_api - INFO - ================================================================================
2025-07-22 16:44:03 - root - INFO - ================================================================================
2025-07-22 16:44:03 - root - INFO - JIRA批量任务创建结果:
2025-07-22 16:44:03 - root - INFO - 总任务数: 1
2025-07-22 16:44:03 - root - INFO - 主任务创建: 0 个
2025-07-22 16:44:03 - root - INFO - 子任务创建: 0 个
2025-07-22 16:44:03 - root - INFO - Sprint关联: 0 个
2025-07-22 16:44:03 - root - INFO - 需求关联: 0 个
2025-07-22 16:44:03 - root - INFO - 成功: 0 个
2025-07-22 16:44:03 - root - INFO - 失败: 2 个
2025-07-22 16:44:03 - root - INFO - 返回结果:
2025-07-22 16:44:03 - root - INFO - {
  "success": true,
  "data": {
    "success": [],
    "failed": [
      {
        "success": false,
        "type": "main_task",
        "title": "PB系统支持退回删除操作员申请",
        "assignee": "吴泳琳",
        "task_type": "主任务",
        "error": "创建主任务失败: JIRA API错误: 400 - ",
        "demand": "JGKEZH-11433",
        "module": "三季度优化",
        "main_task": "PB系统支持退回删除操作员申请",
        "sub_task": "",
        "workload": "1.0"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "web删除操作员逻辑调整",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "吴泳琳",
        "task_type": "API",
        "error": "主任务 'PB系统支持退回删除操作员申请' 创建失败，子任务 'web删除操作员逻辑调整' 无法创建",
        "demand": "JGKEZH-11433",
        "module": "三季度优化",
        "sub_task": "web删除操作员逻辑调整",
        "workload": "1.0"
      }
    ],
    "summary": {
      "total": 1,
      "success_count": 0,
      "failed_count": 2,
      "main_tasks_created": 0,
      "sub_tasks_created": 0,
      "sprint_linked": 0,
      "demands_linked": 0
    }
  }
}
2025-07-22 16:44:03 - root - INFO - ================================================================================
2025-07-22 16:46:13 - root - INFO - 日志系统初始化完成
2025-07-22 16:46:13 - root - INFO - 日志级别: INFO
2025-07-22 16:46:13 - root - INFO - 应用日志文件: logs/app_20250722.log
2025-07-22 16:46:13 - root - INFO - JIRA API日志文件: logs/jira_api_20250722.log
2025-07-22 16:48:17 - root - INFO - 日志系统初始化完成
2025-07-22 16:48:17 - root - INFO - 日志级别: INFO
2025-07-22 16:48:17 - root - INFO - 应用日志文件: logs/app_20250722.log
2025-07-22 16:48:17 - root - INFO - JIRA API日志文件: logs/jira_api_20250722.log
2025-07-22 16:48:57 - root - INFO - ================================================================================
2025-07-22 16:48:57 - root - INFO - 收到JIRA批量任务创建请求:
2025-07-22 16:48:57 - root - INFO - 任务数量: 11
2025-07-22 16:48:57 - root - INFO - 环境: test
2025-07-22 16:48:57 - root - INFO - 用户: lidezheng
2025-07-22 16:48:57 - root - INFO - 项目: JGKEZH
2025-07-22 16:48:57 - root - INFO - Sprint: INST2025-Sprint10
2025-07-22 16:48:57 - root - INFO - JIRA配置 (敏感信息已隐藏):
2025-07-22 16:48:57 - root - INFO - {
  "environment": "test",
  "username": "lidezheng",
  "token": "***",
  "project_key": "JGKEZH",
  "sprint": "INST2025-Sprint10",
  "test_assignee": "zhouqishu"
}
2025-07-22 16:48:57 - root - INFO - 任务详情:
2025-07-22 16:48:57 - root - INFO -   任务 1: 多个场景设定图片上传比例 - 负责人: 关远
2025-07-22 16:48:57 - root - INFO -   任务 2: 发送邮件模板通知调整 - 负责人: 林文杰
2025-07-22 16:48:57 - root - INFO -   任务 3: cms审核支持退回 - 负责人: 关远
2025-07-22 16:48:57 - root - INFO -   任务 4: web删除操作员逻辑调整 - 负责人: 吴泳琳
2025-07-22 16:48:57 - root - INFO -   任务 5: CMS删除操作员逻辑调整 - 负责人: 林文杰
2025-07-22 16:48:57 - root - INFO -   任务 6: 产品目录树列表和筛选 - 负责人: 关远
2025-07-22 16:48:57 - root - INFO -   任务 7: 产品管理新增【关联产品目录树】选项 - 负责人: 关远
2025-07-22 16:48:57 - root - INFO -   任务 8: 产品目录树列表 - 负责人: 林文杰
2025-07-22 16:48:57 - root - INFO -   任务 9: 目录新增&编辑 - 负责人: 林文杰
2025-07-22 16:48:57 - root - INFO -   任务 10: 筛选项新增 类型 - 负责人: 关远
2025-07-22 16:48:57 - root - INFO -   任务 11: 开通列表支持类型筛选 - 负责人: 林文杰
2025-07-22 16:48:57 - root - INFO - ================================================================================
2025-07-22 16:48:57 - jira_api - INFO - ================================================================================
2025-07-22 16:48:57 - jira_api - INFO - 开始按层次结构批量创建JIRA任务
2025-07-22 16:48:57 - jira_api - INFO - 任务数量: 11
2025-07-22 16:48:57 - jira_api - INFO - 环境: test
2025-07-22 16:48:57 - jira_api - INFO - 项目: JGKEZH
2025-07-22 16:48:57 - jira_api - INFO - 用户: lidezheng
2025-07-22 16:48:57 - jira_api - INFO - Sprint: INST2025-Sprint10
2025-07-22 16:48:57 - jira_api - INFO - ================================================================================
2025-07-22 16:48:57 - jira_api - INFO - 开始分析任务层次结构...
2025-07-22 16:48:57 - jira_api - INFO - 发现主任务: CMS图片裁剪功能优化
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: 多个场景设定图片上传比例 -> 主任务: CMS图片裁剪功能优化
2025-07-22 16:48:57 - jira_api - INFO - 发现主任务: CMS代办通知邮件
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: 发送邮件模板通知调整 -> 主任务: CMS代办通知邮件
2025-07-22 16:48:57 - jira_api - INFO - 发现主任务: PB系统支持退回删除操作员申请
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: cms审核支持退回 -> 主任务: PB系统支持退回删除操作员申请
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: web删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: CMS删除操作员逻辑调整 -> 主任务: PB系统支持退回删除操作员申请
2025-07-22 16:48:57 - jira_api - INFO - 发现主任务: CMS端「产品目录树」
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: 产品目录树列表和筛选 -> 主任务: CMS端「产品目录树」
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: 产品管理新增【关联产品目录树】选项 -> 主任务: CMS端「产品目录树」
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: 产品目录树列表 -> 主任务: CMS端「产品目录树」
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: 目录新增&编辑 -> 主任务: CMS端「产品目录树」
2025-07-22 16:48:57 - jira_api - INFO - 发现主任务: CMS「产品管理-开通列表」优化
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: 筛选项新增 类型 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-22 16:48:57 - jira_api - INFO - 发现子任务: 开通列表支持类型筛选 -> 主任务: CMS「产品管理-开通列表」优化
2025-07-22 16:48:57 - jira_api - INFO - 任务结构分析完成: 5 个主任务, 11 个子任务
2025-07-22 16:48:57 - jira_api - INFO - 分析结果: 5 个主任务, 11 个子任务
2025-07-22 16:48:57 - jira_api - INFO - ============================================================
2025-07-22 16:48:57 - jira_api - INFO - 步骤1: 开始创建主任务
2025-07-22 16:48:57 - jira_api - INFO - 需要创建 5 个主任务
2025-07-22 16:48:57 - jira_api - INFO - ============================================================
2025-07-22 16:48:57 - jira_api - INFO - 创建主任务: CMS图片裁剪功能优化
2025-07-22 16:48:58 - jira_api - INFO - ================================================================================
2025-07-22 16:48:58 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:48:58 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:48:58 - jira_api - INFO - 请求方法: POST
2025-07-22 16:48:58 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:48:58 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:48:58 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:48:58 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS图片裁剪功能优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:48:58 - jira_api - INFO - ================================================================================
2025-07-22 16:48:58 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:48:58 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:48:58 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:48:58 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592294x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=203033262872E59E1632337C2FA095DA; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_461a488d085e99d07c101cc7426bb81aad053bb6_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1mloqbu', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:48:58 - jira_api - INFO - 响应体:
2025-07-22 16:48:58 - jira_api - INFO - {
  "id": "857333",
  "key": "JGKEZH-12694",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857333"
}
2025-07-22 16:48:58 - jira_api - INFO - ================================================================================
2025-07-22 16:48:58 - jira_api - INFO - 成功创建main_task: JGKEZH-12694
2025-07-22 16:48:58 - jira_api - INFO - 主任务创建成功: CMS图片裁剪功能优化 -> JGKEZH-12694
2025-07-22 16:48:58 - jira_api - INFO - 创建主任务: CMS代办通知邮件
2025-07-22 16:48:58 - jira_api - INFO - ================================================================================
2025-07-22 16:48:58 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:48:58 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:48:58 - jira_api - INFO - 请求方法: POST
2025-07-22 16:48:58 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:48:58 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:48:58 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:48:58 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS代办通知邮件",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "customfield_13103": {
      "name": "linwenjie"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:48:58 - jira_api - INFO - ================================================================================
2025-07-22 16:48:59 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:48:59 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:48:59 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:48:59 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592295x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=9911E69A20D74DC0F572DEDE990B17A7; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_3d02b135430fbb589812c6f4d2d9225d345037fc_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1c9bml9', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:48:59 - jira_api - INFO - 响应体:
2025-07-22 16:48:59 - jira_api - INFO - {
  "id": "857334",
  "key": "JGKEZH-12695",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857334"
}
2025-07-22 16:48:59 - jira_api - INFO - ================================================================================
2025-07-22 16:48:59 - jira_api - INFO - 成功创建main_task: JGKEZH-12695
2025-07-22 16:48:59 - jira_api - INFO - 主任务创建成功: CMS代办通知邮件 -> JGKEZH-12695
2025-07-22 16:48:59 - jira_api - INFO - 创建主任务: PB系统支持退回删除操作员申请
2025-07-22 16:48:59 - jira_api - INFO - ================================================================================
2025-07-22 16:48:59 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:48:59 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:48:59 - jira_api - INFO - 请求方法: POST
2025-07-22 16:48:59 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:48:59 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:48:59 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:48:59 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "PB系统支持退回删除操作员申请",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:48:59 - jira_api - INFO - ================================================================================
2025-07-22 16:49:00 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:49:00 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:00 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:48:59 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592296x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=9A58688A53D2988EC763F9853D8A8979; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_af7b0b7cef7dc63944ce0677cf9ab8df9decd322_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'umxhc6', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:00 - jira_api - INFO - 响应体:
2025-07-22 16:49:00 - jira_api - INFO - {
  "id": "857335",
  "key": "JGKEZH-12696",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857335"
}
2025-07-22 16:49:00 - jira_api - INFO - ================================================================================
2025-07-22 16:49:00 - jira_api - INFO - 成功创建main_task: JGKEZH-12696
2025-07-22 16:49:00 - jira_api - INFO - 主任务创建成功: PB系统支持退回删除操作员申请 -> JGKEZH-12696
2025-07-22 16:49:00 - jira_api - INFO - 创建主任务: CMS端「产品目录树」
2025-07-22 16:49:00 - jira_api - INFO - ================================================================================
2025-07-22 16:49:00 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:49:00 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:00 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:00 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:00 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:00 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:00 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS端「产品目录树」",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:49:00 - jira_api - INFO - ================================================================================
2025-07-22 16:49:00 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:49:00 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:00 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:00 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592297x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=6A183A5D4B9E110FF7464936D2932788; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_72974a01901b6bb8914122d4d96688834b5cf6ed_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'o9hius', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:00 - jira_api - INFO - 响应体:
2025-07-22 16:49:00 - jira_api - INFO - {
  "id": "857336",
  "key": "JGKEZH-12697",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857336"
}
2025-07-22 16:49:00 - jira_api - INFO - ================================================================================
2025-07-22 16:49:00 - jira_api - INFO - 成功创建main_task: JGKEZH-12697
2025-07-22 16:49:00 - jira_api - INFO - 主任务创建成功: CMS端「产品目录树」 -> JGKEZH-12697
2025-07-22 16:49:00 - jira_api - INFO - 创建主任务: CMS「产品管理-开通列表」优化
2025-07-22 16:49:00 - jira_api - INFO - ================================================================================
2025-07-22 16:49:00 - jira_api - INFO - JIRA API 创建main_task请求详情:
2025-07-22 16:49:00 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:00 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:00 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:00 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:00 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:00 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS「产品管理-开通列表」优化",
    "issuetype": {
      "id": "11007"
    },
    "priority": {
      "id": "3"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "customfield_13103": {
      "name": "guanyuan"
    },
    "customfield_11305": {
      "name": "zhouqishu"
    }
  }
}
2025-07-22 16:49:00 - jira_api - INFO - ================================================================================
2025-07-22 16:49:01 - jira_api - INFO - JIRA API 创建main_task响应详情:
2025-07-22 16:49:01 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:01 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:01 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592298x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=88F87A579FCD2F142EB505DBD6DAA274; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_626193671963ad3fee358a037d8f6c99ee0f00b8_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'd8drxe', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:01 - jira_api - INFO - 响应体:
2025-07-22 16:49:01 - jira_api - INFO - {
  "id": "857337",
  "key": "JGKEZH-12698",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857337"
}
2025-07-22 16:49:01 - jira_api - INFO - ================================================================================
2025-07-22 16:49:01 - jira_api - INFO - 成功创建main_task: JGKEZH-12698
2025-07-22 16:49:01 - jira_api - INFO - 主任务创建成功: CMS「产品管理-开通列表」优化 -> JGKEZH-12698
2025-07-22 16:49:01 - jira_api - INFO - 主任务创建完成: 成功 5 个，失败 0 个
2025-07-22 16:49:01 - jira_api - INFO - ============================================================
2025-07-22 16:49:01 - jira_api - INFO - 步骤2: 开始Sprint关联
2025-07-22 16:49:01 - jira_api - INFO - Sprint名称: INST2025-Sprint10
2025-07-22 16:49:01 - jira_api - INFO - 需要关联 5 个主任务
2025-07-22 16:49:01 - jira_api - INFO - ============================================================
2025-07-22 16:49:01 - jira_api - INFO - 查询Sprint ID: http://jirauat.gf.com.cn/rest/greenhopper/1.0/sprint/picker?query=INST2025-Sprint10
2025-07-22 16:49:01 - jira_api - INFO - Sprint查询请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Accept': 'application/json'}
2025-07-22 16:49:01 - jira_api - INFO - Sprint查询响应状态码: 200
2025-07-22 16:49:01 - jira_api - INFO - Sprint查询响应: {"suggestions":[],"allMatches":[]}
2025-07-22 16:49:01 - jira_api - ERROR - 未找到Sprint: INST2025-Sprint10
2025-07-22 16:49:01 - jira_api - ERROR - 未找到Sprint: INST2025-Sprint10
2025-07-22 16:49:01 - jira_api - INFO - Sprint关联完成: 成功 0 个
2025-07-22 16:49:01 - jira_api - INFO - ============================================================
2025-07-22 16:49:01 - jira_api - INFO - 步骤3: 开始需求关联
2025-07-22 16:49:01 - jira_api - INFO - ============================================================
2025-07-22 16:49:01 - jira_api - INFO - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12694
2025-07-22 16:49:01 - jira_api - INFO - 关联需求 JGKEZH-11433 到任务 JGKEZH-12694
2025-07-22 16:49:01 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12694
2025-07-22 16:49:01 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:49:01 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-22 16:49:01 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:49:01 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:49:01 - jira_api - INFO - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12694
2025-07-22 16:49:01 - jira_api - INFO - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12695
2025-07-22 16:49:01 - jira_api - INFO - 关联需求 JGKEZH-11433 到任务 JGKEZH-12695
2025-07-22 16:49:01 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12695
2025-07-22 16:49:01 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:49:01 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-22 16:49:01 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:49:01 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:49:01 - jira_api - INFO - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12695
2025-07-22 16:49:01 - jira_api - INFO - 关联需求 JGKEZH-11433 到主任务 JGKEZH-12696
2025-07-22 16:49:01 - jira_api - INFO - 关联需求 JGKEZH-11433 到任务 JGKEZH-12696
2025-07-22 16:49:01 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12696
2025-07-22 16:49:01 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:49:01 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11433"
          }
        }
      }
    ]
  }
}
2025-07-22 16:49:02 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:49:02 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:49:02 - jira_api - INFO - 需求 JGKEZH-11433 成功关联到任务 JGKEZH-12696
2025-07-22 16:49:02 - jira_api - INFO - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12697
2025-07-22 16:49:02 - jira_api - INFO - 关联需求 JGKEZH-11521 到任务 JGKEZH-12697
2025-07-22 16:49:02 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12697
2025-07-22 16:49:02 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:49:02 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-22 16:49:02 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:49:02 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:49:02 - jira_api - INFO - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12697
2025-07-22 16:49:02 - jira_api - INFO - 关联需求 JGKEZH-11521 到主任务 JGKEZH-12698
2025-07-22 16:49:02 - jira_api - INFO - 关联需求 JGKEZH-11521 到任务 JGKEZH-12698
2025-07-22 16:49:02 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue/JGKEZH-12698
2025-07-22 16:49:02 - jira_api - INFO - 需求关联请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***', 'Content-Type': 'application/json', 'Accept': 'application/json'}
2025-07-22 16:49:02 - jira_api - INFO - 请求体: {
  "update": {
    "issuelinks": [
      {
        "add": {
          "type": {
            "name": "需求关联",
            "inward": "需求关联",
            "outward": "需求关联"
          },
          "inwardIssue": {
            "key": "JGKEZH-11521"
          }
        }
      }
    ]
  }
}
2025-07-22 16:49:02 - jira_api - INFO - 需求关联响应状态码: 204
2025-07-22 16:49:02 - jira_api - INFO - 需求关联响应: 
2025-07-22 16:49:02 - jira_api - INFO - 需求 JGKEZH-11521 成功关联到任务 JGKEZH-12698
2025-07-22 16:49:02 - jira_api - INFO - 需求关联完成: 成功 5 个
2025-07-22 16:49:02 - jira_api - INFO - ============================================================
2025-07-22 16:49:02 - jira_api - INFO - 步骤4: 开始创建子任务
2025-07-22 16:49:02 - jira_api - INFO - 需要创建 11 个子任务
2025-07-22 16:49:02 - jira_api - INFO - 成功创建的主任务数量: 5
2025-07-22 16:49:02 - jira_api - INFO - ============================================================
2025-07-22 16:49:02 - jira_api - INFO - 有效子任务: 11 个
2025-07-22 16:49:02 - jira_api - INFO - 因主任务失败而跳过的子任务: 0 个
2025-07-22 16:49:02 - jira_api - INFO - 创建子任务 1/11: 多个场景设定图片上传比例 -> 主任务: JGKEZH-12694
2025-07-22 16:49:02 - jira_api - INFO - ================================================================================
2025-07-22 16:49:02 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:02 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:02 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:02 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:02 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:02 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:02 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "多个场景设定图片上传比例",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12694"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-22 16:49:02 - jira_api - INFO - ================================================================================
2025-07-22 16:49:03 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:03 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:03 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:03 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592305x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=7D0DAA44A08A793D4CAC3AF0BC4AE3A3; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_b3ed7b20ae3bc676f963a6ee1dd53d9972b5580e_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'sl4h9e', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:03 - jira_api - INFO - 响应体:
2025-07-22 16:49:03 - jira_api - INFO - {
  "id": "857338",
  "key": "JGKEZH-12699",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857338"
}
2025-07-22 16:49:03 - jira_api - INFO - ================================================================================
2025-07-22 16:49:03 - jira_api - INFO - 成功创建sub_task: JGKEZH-12699
2025-07-22 16:49:03 - jira_api - INFO - 子任务创建成功: 多个场景设定图片上传比例 -> JGKEZH-12699
2025-07-22 16:49:03 - jira_api - INFO - 创建子任务 2/11: 发送邮件模板通知调整 -> 主任务: JGKEZH-12695
2025-07-22 16:49:03 - jira_api - INFO - ================================================================================
2025-07-22 16:49:03 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:03 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:03 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:03 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:03 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:03 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:03 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "发送邮件模板通知调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12695"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-22 16:49:03 - jira_api - INFO - ================================================================================
2025-07-22 16:49:04 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:04 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:04 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:03 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592306x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=65174F3A8EBF56A0DE711B470AB2F8AD; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_d46fd22c1059e5e63dc4d7afe96731b363374cfe_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'gk4ndz', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:04 - jira_api - INFO - 响应体:
2025-07-22 16:49:04 - jira_api - INFO - {
  "id": "857339",
  "key": "JGKEZH-12700",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857339"
}
2025-07-22 16:49:04 - jira_api - INFO - ================================================================================
2025-07-22 16:49:04 - jira_api - INFO - 成功创建sub_task: JGKEZH-12700
2025-07-22 16:49:04 - jira_api - INFO - 子任务创建成功: 发送邮件模板通知调整 -> JGKEZH-12700
2025-07-22 16:49:04 - jira_api - INFO - 创建子任务 3/11: cms审核支持退回 -> 主任务: JGKEZH-12696
2025-07-22 16:49:04 - jira_api - INFO - ================================================================================
2025-07-22 16:49:04 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:04 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:04 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:04 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:04 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:04 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:04 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "cms审核支持退回",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12696"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.1d",
      "remainingEstimate": "0.1d"
    }
  }
}
2025-07-22 16:49:04 - jira_api - INFO - ================================================================================
2025-07-22 16:49:04 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:04 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:04 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:04 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592307x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=C2E0765D6EA2C63AE304594337244938; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_95c19418b79ac3e44753c49122091185c1e96a63_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'ixx0ox', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:04 - jira_api - INFO - 响应体:
2025-07-22 16:49:04 - jira_api - INFO - {
  "id": "857340",
  "key": "JGKEZH-12701",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857340"
}
2025-07-22 16:49:04 - jira_api - INFO - ================================================================================
2025-07-22 16:49:04 - jira_api - INFO - 成功创建sub_task: JGKEZH-12701
2025-07-22 16:49:04 - jira_api - INFO - 子任务创建成功: cms审核支持退回 -> JGKEZH-12701
2025-07-22 16:49:04 - jira_api - INFO - 创建子任务 4/11: web删除操作员逻辑调整 -> 主任务: JGKEZH-12696
2025-07-22 16:49:04 - jira_api - INFO - ================================================================================
2025-07-22 16:49:04 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:04 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:04 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:04 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:04 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:04 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:04 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "web删除操作员逻辑调整",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12696"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "wuyonglin"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-22 16:49:04 - jira_api - INFO - ================================================================================
2025-07-22 16:49:04 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:04 - jira_api - INFO - 响应状态码: 400
2025-07-22 16:49:04 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:04 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592308x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=394DDCA7249966A5D0FEA41C7D4EFAFB; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_2995a572b9a908b91e7ccdc979cbf419317f5196_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '67g33n', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:04 - jira_api - INFO - 响应体:
2025-07-22 16:49:04 - jira_api - INFO - {
  "errorMessages": [],
  "errors": {
    "assignee": "用户 'wuyonglin' 不存在。"
  }
}
2025-07-22 16:49:04 - jira_api - INFO - ================================================================================
2025-07-22 16:49:04 - jira_api - ERROR - 创建sub_task失败: JIRA API错误: 400 - 
2025-07-22 16:49:04 - jira_api - ERROR - 创建sub_task异常: JIRA API错误: 400 - 
2025-07-22 16:49:04 - jira_api - ERROR - 子任务 'web删除操作员逻辑调整' 创建失败: 创建子任务失败: JIRA API错误: 400 - 
2025-07-22 16:49:04 - jira_api - INFO - 创建子任务 5/11: CMS删除操作员逻辑调整 -> 主任务: JGKEZH-12696
2025-07-22 16:49:05 - jira_api - INFO - ================================================================================
2025-07-22 16:49:05 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:05 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:05 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:05 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:05 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:05 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:05 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "CMS删除操作员逻辑调整",
    "issuetype": {
      "id": "11015"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12696"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-22 16:49:05 - jira_api - INFO - ================================================================================
2025-07-22 16:49:05 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:05 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:05 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:05 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592309x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=6ED9B8243C673E81C68DD526C4B9FB06; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_3df3f69a749c9d77ee8387c3f6fecd831dd21743_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '13k1f1w', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:05 - jira_api - INFO - 响应体:
2025-07-22 16:49:05 - jira_api - INFO - {
  "id": "857341",
  "key": "JGKEZH-12702",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857341"
}
2025-07-22 16:49:05 - jira_api - INFO - ================================================================================
2025-07-22 16:49:05 - jira_api - INFO - 成功创建sub_task: JGKEZH-12702
2025-07-22 16:49:05 - jira_api - INFO - 子任务创建成功: CMS删除操作员逻辑调整 -> JGKEZH-12702
2025-07-22 16:49:05 - jira_api - INFO - 创建子任务 6/11: 产品目录树列表和筛选 -> 主任务: JGKEZH-12697
2025-07-22 16:49:05 - jira_api - INFO - ================================================================================
2025-07-22 16:49:05 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:05 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:05 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:05 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:05 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:05 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:05 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表和筛选",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12697"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "1.0d",
      "remainingEstimate": "1.0d"
    }
  }
}
2025-07-22 16:49:05 - jira_api - INFO - ================================================================================
2025-07-22 16:49:06 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:06 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:06 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:06 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592310x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=DB8D1811ADC297F21F6F67F49EF42010; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_5332076ebbcffcb319b5474e5126855d93d9f216_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '16o014w', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:06 - jira_api - INFO - 响应体:
2025-07-22 16:49:06 - jira_api - INFO - {
  "id": "857342",
  "key": "JGKEZH-12703",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857342"
}
2025-07-22 16:49:06 - jira_api - INFO - ================================================================================
2025-07-22 16:49:06 - jira_api - INFO - 成功创建sub_task: JGKEZH-12703
2025-07-22 16:49:06 - jira_api - INFO - 子任务创建成功: 产品目录树列表和筛选 -> JGKEZH-12703
2025-07-22 16:49:06 - jira_api - INFO - 创建子任务 7/11: 产品管理新增【关联产品目录树】选项 -> 主任务: JGKEZH-12697
2025-07-22 16:49:06 - jira_api - INFO - ================================================================================
2025-07-22 16:49:06 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:06 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:06 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:06 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:06 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:06 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:06 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品管理新增【关联产品目录树】选项",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12697"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-22 16:49:06 - jira_api - INFO - ================================================================================
2025-07-22 16:49:07 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:07 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:07 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:06 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592311x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=77806D6529AAA2A77C4132BA300543FA; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_b93f5302da0a6be445ad6ac009465b98d3c26bf0_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1k7rzo4', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:07 - jira_api - INFO - 响应体:
2025-07-22 16:49:07 - jira_api - INFO - {
  "id": "857343",
  "key": "JGKEZH-12704",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857343"
}
2025-07-22 16:49:07 - jira_api - INFO - ================================================================================
2025-07-22 16:49:07 - jira_api - INFO - 成功创建sub_task: JGKEZH-12704
2025-07-22 16:49:07 - jira_api - INFO - 子任务创建成功: 产品管理新增【关联产品目录树】选项 -> JGKEZH-12704
2025-07-22 16:49:07 - jira_api - INFO - 创建子任务 8/11: 产品目录树列表 -> 主任务: JGKEZH-12697
2025-07-22 16:49:07 - jira_api - INFO - ================================================================================
2025-07-22 16:49:07 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:07 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:07 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:07 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:07 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:07 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:07 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "产品目录树列表",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12697"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-22 16:49:07 - jira_api - INFO - ================================================================================
2025-07-22 16:49:07 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:07 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:07 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:07 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592312x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=90A687162775B81A539C51CB9290CADC; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_f1bde527f4bfc6b7e281e3564385d8b42ba8aee5_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'hj8b4x', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:07 - jira_api - INFO - 响应体:
2025-07-22 16:49:07 - jira_api - INFO - {
  "id": "857344",
  "key": "JGKEZH-12705",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857344"
}
2025-07-22 16:49:07 - jira_api - INFO - ================================================================================
2025-07-22 16:49:07 - jira_api - INFO - 成功创建sub_task: JGKEZH-12705
2025-07-22 16:49:07 - jira_api - INFO - 子任务创建成功: 产品目录树列表 -> JGKEZH-12705
2025-07-22 16:49:07 - jira_api - INFO - 创建子任务 9/11: 目录新增&编辑 -> 主任务: JGKEZH-12697
2025-07-22 16:49:07 - jira_api - INFO - ================================================================================
2025-07-22 16:49:07 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:07 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:07 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:07 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:07 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:07 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:07 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "目录新增&编辑",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12697"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.5d",
      "remainingEstimate": "0.5d"
    }
  }
}
2025-07-22 16:49:07 - jira_api - INFO - ================================================================================
2025-07-22 16:49:08 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:08 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:08 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:08 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592313x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=094FC0F30D159D6E3952456B3A8383E0; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_83f9300daabdf50aa92da16b5727d49dbd0679d2_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': 'qaizo4', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:08 - jira_api - INFO - 响应体:
2025-07-22 16:49:08 - jira_api - INFO - {
  "id": "857345",
  "key": "JGKEZH-12706",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857345"
}
2025-07-22 16:49:08 - jira_api - INFO - ================================================================================
2025-07-22 16:49:08 - jira_api - INFO - 成功创建sub_task: JGKEZH-12706
2025-07-22 16:49:08 - jira_api - INFO - 子任务创建成功: 目录新增&编辑 -> JGKEZH-12706
2025-07-22 16:49:08 - jira_api - INFO - 创建子任务 10/11: 筛选项新增 类型 -> 主任务: JGKEZH-12698
2025-07-22 16:49:08 - jira_api - INFO - ================================================================================
2025-07-22 16:49:08 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:08 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:08 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:08 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:08 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:08 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:08 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "筛选项新增 类型",
    "issuetype": {
      "id": "11012"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12698"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "guanyuan"
    },
    "timetracking": {
      "originalEstimate": "0.2d",
      "remainingEstimate": "0.2d"
    }
  }
}
2025-07-22 16:49:08 - jira_api - INFO - ================================================================================
2025-07-22 16:49:09 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:09 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:09 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:08 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592314x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=D6ECDB080EBF1EAA73B674A4D2453E39; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_b0df994aa37fd94ceea1f608048171cf2504bdb5_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1a0sdrx', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:09 - jira_api - INFO - 响应体:
2025-07-22 16:49:09 - jira_api - INFO - {
  "id": "857346",
  "key": "JGKEZH-12707",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857346"
}
2025-07-22 16:49:09 - jira_api - INFO - ================================================================================
2025-07-22 16:49:09 - jira_api - INFO - 成功创建sub_task: JGKEZH-12707
2025-07-22 16:49:09 - jira_api - INFO - 子任务创建成功: 筛选项新增 类型 -> JGKEZH-12707
2025-07-22 16:49:09 - jira_api - INFO - 创建子任务 11/11: 开通列表支持类型筛选 -> 主任务: JGKEZH-12698
2025-07-22 16:49:09 - jira_api - INFO - ================================================================================
2025-07-22 16:49:09 - jira_api - INFO - JIRA API 创建sub_task请求详情:
2025-07-22 16:49:09 - jira_api - INFO - 请求URL: http://jirauat.gf.com.cn/rest/api/latest/issue
2025-07-22 16:49:09 - jira_api - INFO - 请求方法: POST
2025-07-22 16:49:09 - jira_api - INFO - 认证用户: lidezheng
2025-07-22 16:49:09 - jira_api - INFO - 请求头: {'Authorization': 'Bearer MjE5Mzk1Mj***'}
2025-07-22 16:49:09 - jira_api - INFO - 请求体 (JSON):
2025-07-22 16:49:09 - jira_api - INFO - {
  "fields": {
    "project": {
      "key": "JGKEZH"
    },
    "summary": "开通列表支持类型筛选",
    "issuetype": {
      "id": "11013"
    },
    "priority": {
      "id": "3"
    },
    "parent": {
      "key": "JGKEZH-12698"
    },
    "reporter": {
      "name": "lidezheng"
    },
    "assignee": {
      "name": "linwenjie"
    },
    "timetracking": {
      "originalEstimate": "0.3d",
      "remainingEstimate": "0.3d"
    }
  }
}
2025-07-22 16:49:09 - jira_api - INFO - ================================================================================
2025-07-22 16:49:10 - jira_api - INFO - JIRA API 创建sub_task响应详情:
2025-07-22 16:49:10 - jira_api - INFO - 响应状态码: 201
2025-07-22 16:49:10 - jira_api - INFO - 响应头: {'Server': 'nginx/1.10.2', 'Date': 'Tue, 22 Jul 2025 08:49:09 GMT', 'Content-Type': 'application/json;charset=UTF-8', 'Transfer-Encoding': 'chunked', 'Connection': 'keep-alive', 'X-AREQUESTID': '1011x592315x1', 'Referrer-Policy': 'strict-origin-when-cross-origin', 'X-XSS-Protection': '1; mode=block', 'X-Content-Type-Options': 'nosniff', 'X-Frame-Options': 'SAMEORIGIN', 'Content-Security-Policy': 'sandbox', 'Strict-Transport-Security': 'max-age=31536000', 'Set-Cookie': 'JSESSIONID=1D558D91AD43C18C5F97D30D33047C7E; Path=/; HttpOnly, atlassian.xsrf.token=B7JB-OB8V-D1UF-C7XY_fd5dd3352433821f58c3368bd6c4bacd88aad962_lin; Path=/', 'X-Seraph-LoginReason': 'OK', 'X-ASESSIONID': '1qodmyb', 'X-AUSERNAME': 'lidezheng', 'Cache-Control': 'no-cache, no-store, no-transform', 'Content-Encoding': 'gzip', 'Vary': 'User-Agent'}
2025-07-22 16:49:10 - jira_api - INFO - 响应体:
2025-07-22 16:49:10 - jira_api - INFO - {
  "id": "857347",
  "key": "JGKEZH-12708",
  "self": "http://jirauat.gf.com.cn/rest/api/latest/issue/857347"
}
2025-07-22 16:49:10 - jira_api - INFO - ================================================================================
2025-07-22 16:49:10 - jira_api - INFO - 成功创建sub_task: JGKEZH-12708
2025-07-22 16:49:10 - jira_api - INFO - 子任务创建成功: 开通列表支持类型筛选 -> JGKEZH-12708
2025-07-22 16:49:10 - jira_api - INFO - 子任务创建完成: 成功 10 个，失败 1 个
2025-07-22 16:49:10 - jira_api - INFO - ================================================================================
2025-07-22 16:49:10 - jira_api - INFO - JIRA 批量任务创建完成:
2025-07-22 16:49:10 - jira_api - INFO - 总任务数: 11
2025-07-22 16:49:10 - jira_api - INFO - 主任务创建: 5 个
2025-07-22 16:49:10 - jira_api - INFO - 子任务创建: 10 个
2025-07-22 16:49:10 - jira_api - INFO - Sprint关联: 0 个
2025-07-22 16:49:10 - jira_api - INFO - 需求关联: 5 个
2025-07-22 16:49:10 - jira_api - INFO - 成功: 15 个
2025-07-22 16:49:10 - jira_api - INFO - 失败: 2 个
2025-07-22 16:49:10 - jira_api - INFO - ================================================================================
2025-07-22 16:49:10 - root - INFO - ================================================================================
2025-07-22 16:49:10 - root - INFO - JIRA批量任务创建结果:
2025-07-22 16:49:10 - root - INFO - 总任务数: 11
2025-07-22 16:49:10 - root - INFO - 主任务创建: 5 个
2025-07-22 16:49:10 - root - INFO - 子任务创建: 10 个
2025-07-22 16:49:10 - root - INFO - Sprint关联: 0 个
2025-07-22 16:49:10 - root - INFO - 需求关联: 5 个
2025-07-22 16:49:10 - root - INFO - 成功: 15 个
2025-07-22 16:49:10 - root - INFO - 失败: 2 个
2025-07-22 16:49:10 - root - INFO - 返回结果:
2025-07-22 16:49:10 - root - INFO - {
  "success": true,
  "data": {
    "success": [
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12694",
        "title": "CMS图片裁剪功能优化",
        "assignee": "关远",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12694"
      },
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12695",
        "title": "CMS代办通知邮件",
        "assignee": "林文杰",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12695"
      },
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12696",
        "title": "PB系统支持退回删除操作员申请",
        "assignee": "关远",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12696"
      },
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12697",
        "title": "CMS端「产品目录树」",
        "assignee": "关远",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12697"
      },
      {
        "success": true,
        "type": "main_task",
        "key": "JGKEZH-12698",
        "title": "CMS「产品管理-开通列表」优化",
        "assignee": "关远",
        "task_type": "主任务",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12698"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12699",
        "title": "多个场景设定图片上传比例",
        "parent_key": "JGKEZH-12694",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12699"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12700",
        "title": "发送邮件模板通知调整",
        "parent_key": "JGKEZH-12695",
        "assignee": "林文杰",
        "task_type": "API",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12700"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12701",
        "title": "cms审核支持退回",
        "parent_key": "JGKEZH-12696",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12701"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12702",
        "title": "CMS删除操作员逻辑调整",
        "parent_key": "JGKEZH-12696",
        "assignee": "林文杰",
        "task_type": "测试",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12702"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12703",
        "title": "产品目录树列表和筛选",
        "parent_key": "JGKEZH-12697",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12703"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12704",
        "title": "产品管理新增【关联产品目录树】选项",
        "parent_key": "JGKEZH-12697",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12704"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12705",
        "title": "产品目录树列表",
        "parent_key": "JGKEZH-12697",
        "assignee": "林文杰",
        "task_type": "API",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12705"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12706",
        "title": "目录新增&编辑",
        "parent_key": "JGKEZH-12697",
        "assignee": "林文杰",
        "task_type": "API",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12706"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12707",
        "title": "筛选项新增 类型",
        "parent_key": "JGKEZH-12698",
        "assignee": "关远",
        "task_type": "UI",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12707"
      },
      {
        "success": true,
        "type": "sub_task",
        "key": "JGKEZH-12708",
        "title": "开通列表支持类型筛选",
        "parent_key": "JGKEZH-12698",
        "assignee": "林文杰",
        "task_type": "API",
        "url": "http://jirauat.gf.com.cn/browse/JGKEZH-12708"
      }
    ],
    "failed": [
      {
        "success": false,
        "type": "sprint_link",
        "title": "Sprint关联",
        "error": "未找到Sprint: INST2025-Sprint10"
      },
      {
        "success": false,
        "type": "sub_task",
        "title": "web删除操作员逻辑调整",
        "main_task": "PB系统支持退回删除操作员申请",
        "assignee": "吴泳琳",
        "task_type": "API",
        "error": "创建子任务失败: JIRA API错误: 400 - ",
        "demand": "JGKEZH-11433",
        "module": "三季度优化",
        "sub_task": "web删除操作员逻辑调整",
        "workload": "1.0"
      }
    ],
    "summary": {
      "total": 11,
      "success_count": 15,
      "failed_count": 2,
      "main_tasks_created": 5,
      "sub_tasks_created": 10,
      "sprint_linked": 0,
      "demands_linked": 5
    }
  }
}
2025-07-22 16:49:10 - root - INFO - ================================================================================
2025-07-22 16:50:36 - root - INFO - 日志系统初始化完成
2025-07-22 16:50:36 - root - INFO - 日志级别: INFO
2025-07-22 16:50:36 - root - INFO - 应用日志文件: logs/app_20250722.log
2025-07-22 16:50:36 - root - INFO - JIRA API日志文件: logs/jira_api_20250722.log
