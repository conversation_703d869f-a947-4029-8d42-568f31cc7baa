# 多阶段构建的Dockerfile
# 第一阶段：构建阶段
FROM docker2.gf.com.cn/library/python:v1.0.0-3.11-ubuntu20.04 as builder

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖到临时目录
RUN pip install --no-cache-dir --user -r requirements.txt

# 第二阶段：运行阶段
FROM docker2.gf.com.cn/library/python:v1.0.0-3.11-ubuntu20.04

# 设置元数据
LABEL maintainer="JIRA Task Creator Team"
LABEL version="1.0"
LABEL description="JIRA任务批量创建工具"

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 从构建阶段复制Python包
COPY --from=builder /root/.local /home/<USER>/.local

# 复制应用代码
COPY . .

# 创建必要的目录并设置权限
RUN mkdir -p logs app/templates uploads \
    && chown -R appuser:appuser /app \
    && chmod +x run.py \
    && chmod 755 logs

# 切换到非root用户
USER appuser

# 确保日志目录权限在运行时也正确
RUN mkdir -p /app/logs

# 设置环境变量
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV ENVIRONMENT=production
ENV APP_HOST=0.0.0.0
ENV APP_PORT=8000

# 暴露端口
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令
CMD ["python", "run.py"] 